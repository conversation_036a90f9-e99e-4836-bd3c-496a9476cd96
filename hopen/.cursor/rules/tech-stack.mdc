---
description: 
globs: 
alwaysApply: true
---
- Use Flutter for the framework (Android and iOS).

## Backend Stack
- Use Go with Gin (or Echo) HTTP router + stdlib net/http for the backend core service layer.
- Use pgx + sqlc for type-safe PostgreSQL queries with pgxpool for connection pooling.
- Use robfig/cron/v3 for scheduled jobs.
- Use NATS JetStream for pub/sub and async workflows.
- Use OpenTelemetry + Prometheus client for tracing/metrics.
- Use Uber-zap for structured logging.
- Use go-playground/validator + JSON-schema for request validation.
- Use Valkey-backed token-bucket for rate-limits.
- Use Kubernetes/Helm manifests for deployment.

## Authentication & Policy (Ory Stack)
- Use Kratos for identity management (password, Social, WebAuthn).
- Use Hydra for OAuth2/OIDC provider (tokens for mobile & third-party apps).
- Use Oathkeeper for edge authoriser/proxy; verifies Hydra tokens or Kratos session cookie, injects identity headers, does mTLS to services.
- Use Keto for RBAC/ABAC; Oathkeeper consults <PERSON><PERSON>'s "is-allowed" endpoint for fine-grained policy.
- We will use 3 options for authentication: Google sign-in button, Apple sign-in button, and authentication via email and password.

## Supporting Infrastructure
- Everything is containerized with Docker.
- Use Docker for testing and deployment.
- Use PostgreSQL for the database.
- Use Caddy or Nginx-QUIC as HTTP/3 front-end (optional Cloudflare edge).
- Use MinIO (S3) for object storage for files (user-uploaded content).
- Use Valkey for caching, session management, rate-limit data, and performance optimization.
- Use EMQX MQTT 5 for real-time chat, bridged to NATS.
- Use Grafana + Loki + Promtail for logs; Prometheus for metrics; Jaeger for traces.
- Use Firebase Cloud Messaging (FCM) for push notifications. For push notifications concerning a specific user, we will use Token-based messaging. We will use Topic-based messaging when we will send push notifications for a wide array of users at the same time.
- Use WebRTC for real-time audio and video communication.
- Use Drift for persistent local storage
- Use MQTT5 for in-app notifications.
- Use MQTT5 for real-time messaging.
- Use this : https://pub.dev/packages/mqtt5_client on the app, and run https://github.com/emqx/emqx (EMQX Open Source Edition) as the MQTT server. To handle auth, we send the user's JWT as the MQTT user password, and then use EMQX API Auth to send a POST to our backend that validates the user's JWT and authenticates them on EMQX
- Use NATS.io for message queuing and async workflows.
- Use the Twelve-Factor App methodology.
- AWS SES for email verification's TOTP (Time based One Time Password).

- Use a clean architecture.

- Use GitHub Actions CI/CD tool to automate testing, building, and deploying Docker containers to Kubernetes (blue/green deployment).
- Use PostHog for analytics.
- Use OVHcloud as the cloud hosting provider (VPS, Virtual private server) for self-hosting the Go backend, MinIO, Valkey and WebRTC.
- Use Viaduc to register the domain hopenapp.com and configure DNS with OVHcloud's DNS services or reliability and low-latency resolution.

When the app scales :
- Use Prometheus (for metrics) paired with Grafana (for vizualization) for monitoring.
- Use OVHcloud's load balancer (or a self-hosted solution like HAProxy) with Docker to distribute traffic across multiple instances (e.g., additional d2-8 servers) as MAU grows.
- Use Let's Encrypt for SSL/TLS, and use OVHcloud's DDoS protection (included in some plans).
- Use automated backups with pgBackRest for PostgreSQL and MinIO's lifecycle policies, stored on a separate OVHcloud volume or offsite, ensuring data resilience.

- We aim : maintainability, dependability, efficiency, speed, security, relialability, usability, efficiency, scalability.


