---
description: 
globs: 
alwaysApply: true
---
# SafeArea Usage Guidelines

- **Purpose:** Use the `SafeArea` widget to ensure application UI elements do not overlap with system intrusions like notches, status bars, home indicators, or other system UI elements on iOS and Android devices.

- **Mandatory Usage:**
    - Wrap the **main content area** (typically the `body` of a `Scaffold`) of all top-level page/screen widgets with `SafeArea`.
    - If a page includes a `bottomNavigationBar` or a persistent bottom sheet, wrap that specific bottom widget with `SafeArea` as well. This prevents the navigation bar from rendering underneath system navigation gestures (like the iOS home indicator).

- **Example Implementation (`Scaffold` Body):**

    ```dart
    // ✅ DO: Wrap the main body content
    Scaffold(
      appBar: AppBar(...),
      body: SafeArea(
        child: Column( // Or ListView, SingleChildScrollView, etc.
          children: [
            // Your page content widgets here
          ],
        ),
      ),
      // ...
    )
    

- **Example Implementation (`bottomNavigationBar`):**

    ```dart
    // ✅ DO: Wrap the bottom navigation bar
    Scaffold(
      // ... appBar and body ...
      bottomNavigationBar: SafeArea(
        child: BottomNavigationBar(
          items: [
            // Your BottomNavigationBarItems here
          ],
        ),
      ),
    )

    // ✅ DO: Wrap custom bottom navigation bars (e.g., Container + Row)
    Scaffold(
      // ... appBar and body ...
      bottomNavigationBar: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          color: Colors.transparent,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              // Your custom icon widgets here
            ],
          ),
        ),
      ),
    )
    

- **Selective Disabling (Use with Caution):**
    - By default, `SafeArea` applies padding to all sides (top, bottom, left, right) affected by system intrusions.
    - You can selectively disable padding for specific sides if needed for a particular design (e.g., a full-bleed background image that *should* extend under the status bar).
    - **Use cases are rare.** Prefer default `SafeArea` behavior unless explicitly required by the design.

    ```dart
    // ⚠️ Use only if necessary: Disabling top padding
    SafeArea(
      top: false, // Allows content to go under the status bar
      child: YourContentWidget(),
    )

    // ⚠️ Use only if necessary: Disabling bottom padding
    SafeArea(
      bottom: false, // Allows content to go under the home indicator/navbar
      child: YourContentWidget(),
    )
    

- **Verification:** Always test pages on various devices (or simulators/emulators) with different screen sizes, aspect ratios, and system intrusions (notches, islands) to ensure `SafeArea` is working correctly.
