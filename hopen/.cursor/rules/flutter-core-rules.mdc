---
description: 
globs: 
alwaysApply: true
---
- Adapt to existing project architecture while maintaining clean code principles.
- Use Flutter 3.x features.
- Implement proper null safety practices.
- Follow proper naming conventions.
- Use proper widget composition.
- Keep widgets small and focused.
- Use const constructors when possible.
- Implement proper widget keys.
- Follow proper layout principles.
- Implement clean architecture with BLoC pattern.
- Follow proper state management principles.
- Use proper dependency injection.
- Implement proper error handling.
- Follow proper state management with BLoC.
- Follow platform-specific design guidelines.
- Use proper localization techniques.
- Follow proper widget composition.
- Keep widgets small and focused.
- Implement proper routing using GoRouter.
- Use proper form validation.
- Implement proper error boundaries.
- Follow proper accessibility guidelines.
- Use proper image caching.
- Implement proper list view optimization.
- Use proper build methods optimization.
- Follow proper state management patterns.
- Implement proper memory management.
- Use proper platform channels when needed.
- Follow proper compilation optimization techniques.