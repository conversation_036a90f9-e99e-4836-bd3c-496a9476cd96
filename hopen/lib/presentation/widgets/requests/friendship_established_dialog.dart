import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../utils/color_transition_controller.dart';
import '../animated_gradient_text.dart';

/// A dialog that appears when another user has accepted the viewing user's request to become friends.
///
/// This dialog is shown when the viewing user has completed the FriendsChoiceDialog before the other user,
/// and the other user has now also completed their choice, establishing the friendship.
///
/// Features:
/// * Displays the new friend's profile picture and name
/// * Shows the timestamp of when the friendship was established
/// * Provides a single "OK" button to acknowledge the new friendship
/// * Uses the same design pattern and UI style as the existing dialogs
/// * Includes animations and transitions consistent with other dialogs
class FriendshipEstablishedDialog extends StatefulWidget {
  const FriendshipEstablishedDialog({
    required this.friendId,
    required this.friendName,
    required this.establishedTimestamp,
    super.key,
    this.friendUsername,
    this.friendProfilePicUrl,
  });

  final String friendId;
  final String friendName;
  final String? friendUsername;
  final String? friendProfilePicUrl;
  final DateTime establishedTimestamp;

  /// Shows the friendship established dialog.
  static Future<bool?> show(
    BuildContext context, {
    required String friendId,
    required String friendName,
    required DateTime establishedTimestamp,
    String? friendUsername,
    String? friendProfilePicUrl,
  }) => showDialog<bool>(
    context: context,
    barrierDismissible: false, // Make the dialog non-dismissible
    barrierColor: Colors.black.withValues(alpha: 0.9),
    builder: (context) => WillPopScope(
      // Prevent back button from dismissing the dialog
      onWillPop: () async => false,
      child: FriendshipEstablishedDialog(
        friendId: friendId,
        friendName: friendName,
        friendUsername: friendUsername,
        friendProfilePicUrl: friendProfilePicUrl,
        establishedTimestamp: establishedTimestamp,
      ),
    ),
  );

  @override
  State<FriendshipEstablishedDialog> createState() => _FriendshipEstablishedDialogState();
}

class _FriendshipEstablishedDialogState extends State<FriendshipEstablishedDialog> {
  /// Controller for managing color transitions
  late ColorTransitionController _colorController;

  @override
  void initState() {
    super.initState();
    _colorController = ColorTransitionController();
    _colorController.startColorLoop();

    // Listen to color changes and update the UI
    _colorController.addListener(() {
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _colorController.dispose();
    super.dispose();
  }

  double _getImageSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 370) {
      return 120;
    } else if (width < 600) {
      return 140;
    } else {
      return 150;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d').format(timestamp);
    }
  }

  @override
  Widget build(BuildContext context) => _buildDialogContent(context);

  Widget _buildDialogContent(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    final imageSize = _getImageSize(context);
    final verticalSpacing = ColorTransitionController.getDialogVerticalSpacing(
      context,
    );

    return AlertDialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
      insetPadding: EdgeInsets.zero,
      titlePadding: const EdgeInsets.only(top: 16),
      contentPadding: const EdgeInsets.symmetric(),
      title: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedGradientText(
              text: 'New friendship!',
              style: TextStyle(
                fontFamily: 'Omnes',
                fontSize: ColorTransitionController.getTitleSize(context),
                fontWeight: FontWeight.bold,
              ),
              colors: const <Color>[
                Color(0xFFFF00FF), // #f0f
                Color(0xFFFB43BB), // #fb43bb
                Color(0xFFF3C935), // #f3c935
                Color(0xFFF0FF00), // #f0ff00
                Color(0xFFC4FF2D), // #c4ff2d
                Color(0xFF91FF64), // #91ff64
                Color(0xFF64FF93), // #64ff93
                Color(0xFF40FFBA), // #40ffba
                Color(0xFF24FFD8), // #24ffd8
                Color(0xFF10FFED), // #10ffed
                Color(0xFF04FFFA), // #04fffa
                Color(0xFF00FFFF), // aqua
              ],
              stops: const <double>[
                0,
                0.12,
                0.37,
                0.48,
                0.53,
                0.6,
                0.67,
                0.74,
                0.81,
                0.88,
                0.94,
                1,
              ],
              duration: const Duration(seconds: 8),
            ),
            SizedBox(height: verticalSpacing * 8),
            _buildFriendProfile(context, imageSize * 1.4),
            SizedBox(height: verticalSpacing * 4),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                'You and ${widget.friendName} are now friends!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: ColorTransitionController.getSubtitleSize(context),
                  height: 1.4, // Added line height for readability
                ),
              ),
            ),
            SizedBox(height: verticalSpacing * 0.8),
            Text(
              'Friendship established ${_formatTimestamp(widget.establishedTimestamp)}',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white54,
                fontSize:
                    ColorTransitionController.getSubtitleSize(context) * 0.8,
              ),
            ),
          ],
        ),
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: screenHeight * 0.1,
      ),
      actionsPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        SizedBox(
          width: double.infinity,
          height: 50,
          child: _buildOkButton(context),
        ),
      ],
    );
  }

  Widget _buildFriendProfile(BuildContext context, double imageSize) =>
      Container(
        width: imageSize,
        height: imageSize,
        decoration: ShapeDecoration(
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(imageSize * 0.4),
            side: BorderSide(
              color: Colors.white.withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          color: Colors.white.withValues(alpha: 0.1),
        ),
        child: widget.friendProfilePicUrl != null &&
                widget.friendProfilePicUrl!.isNotEmpty
            ? ClipRRect(
                borderRadius: BorderRadius.circular(imageSize * 0.4),
                child: Image.network(
                  widget.friendProfilePicUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) =>
                      _buildFallbackAvatar(imageSize),
                ),
              )
            : _buildFallbackAvatar(imageSize),
      );

  Widget _buildFallbackAvatar(double imageSize) => Center(
        child: Text(
          widget.friendName.isNotEmpty
              ? widget.friendName[0].toUpperCase()
              : '?',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: imageSize * 0.4,
          ),
        ),
      );

  Widget _buildOkButton(BuildContext context) => ElevatedButton(
        onPressed: () => Navigator.of(context).pop(true),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue, // Blue color for acknowledgment
          foregroundColor: Colors.white,
          shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(18)),
          padding: EdgeInsets.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ).copyWith(
          elevation: WidgetStateProperty.all(0), // Explicitly set elevation to 0
        ),
        child: const Center(
          child: Text(
            'OK',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ),
      );
}
