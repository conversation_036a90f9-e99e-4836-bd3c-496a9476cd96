<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- ✅ Allow HTTP traffic to Docker backend for development -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Your Docker host IP -->
        <domain includeSubdomains="false">*********</domain>
        <!-- Localhost for emulator testing -->
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">127.0.0.1</domain>
        <domain includeSubdomains="false">********</domain>
        <domain includeSubdomains="false">************0</domain>
        <domain includeSubdomains="false">************</domain>
    </domain-config>
    
    <!-- ✅ Default secure configuration for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config> 