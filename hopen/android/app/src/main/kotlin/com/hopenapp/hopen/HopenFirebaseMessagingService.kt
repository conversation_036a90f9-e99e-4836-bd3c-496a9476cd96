package com.hopenapp.hopen

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import android.app.ActivityManager
import android.content.Context
import androidx.core.app.NotificationManagerCompat
import android.media.RingtoneManager
import java.util.concurrent.atomic.AtomicInteger

class HopenFirebaseMessagingService : FirebaseMessagingService() {
    companion object {
        private const val TAG = "HopenFCM"
        private const val CHANNEL_ID = "hopen_calls"
        private const val CHANNEL_NAME = "Incoming Calls"
        private const val NOTIFICATION_ID = 1000
        private val notificationId = AtomicInteger(0)
        
        // Intent action for Flutter method channel
        const val ACTION_INCOMING_CALL = "com.hopenapp.hopen.INCOMING_CALL"
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Log.d(TAG, "From: ${remoteMessage.from}")
        
        // Check if message contains a data payload
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
            
            val messageType = remoteMessage.data["type"]
            
            if (messageType == "incoming_call") {
                handleIncomingCall(remoteMessage.data)
            } else {
                // Process other message types if needed
                Log.d(TAG, "Received non-call message type: $messageType")
            }
        }
    }
    
    private fun handleIncomingCall(data: Map<String, String>) {
        try {
            // Extract call information
            val callId = data["call_id"] ?: return
            val callerId = data["caller_id"] ?: return
            val callerName = data["caller_name"] ?: "Unknown Caller"
            val callerAvatar = data["caller_avatar"]
            val targetType = data["target_type"] ?: "user"
            val targetId = data["target_id"]
            val targetName = data["target_name"]
            val targetAvatarUrl = data["target_avatar_url"]
            val sdpOffer = data["sdp_offer"]
            val isVideoOffered = data["video_offered"]?.toBoolean() ?: false
            val isAudioOffered = data["audio_offered"]?.toBoolean() ?: true
            val isScreenShareOffered = data["screen_share_offered"]?.toBoolean() ?: false
            
            // Determine if the app is in the foreground
            val isAppInForeground = isAppForeground()
            
            if (isAppInForeground) {
                // App is in foreground, send the call info directly to the Flutter app
                // via the main activity's intent
                sendCallInfoToFlutter(data)
            } else {
                // App is in background or closed, show a high-priority notification
                showIncomingCallNotification(
                    callId,
                    callerId,
                    callerName,
                    callerAvatar,
                    targetType,
                    targetId,
                    targetName,
                    targetAvatarUrl,
                    sdpOffer,
                    isVideoOffered,
                    isAudioOffered,
                    isScreenShareOffered
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling incoming call: ${e.message}", e)
        }
    }
    
    private fun isAppForeground(): Boolean {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val appProcesses = activityManager.runningAppProcesses ?: return false
        val packageName = packageName
        
        for (appProcess in appProcesses) {
            if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND 
                && appProcess.processName == packageName) {
                return true
            }
        }
        return false
    }
    
    private fun sendCallInfoToFlutter(callData: Map<String, String>) {
        // Create an intent to broadcast within the app
        val intent = Intent(ACTION_INCOMING_CALL)
        
        // Add all call data as extras
        for ((key, value) in callData) {
            intent.putExtra(key, value)
        }
        
        // Send broadcast
        sendBroadcast(intent)
        
        Log.d(TAG, "Sent call info to Flutter via broadcast")
    }
    
    private fun showIncomingCallNotification(
        callId: String,
        callerId: String,
        callerName: String,
        callerAvatar: String?,
        targetType: String,
        targetId: String?,
        targetName: String?,
        targetAvatarUrl: String?,
        sdpOffer: String?,
        isVideoOffered: Boolean,
        isAudioOffered: Boolean,
        isScreenShareOffered: Boolean
    ) {
        createNotificationChannel()
        
        // Setup intent for when the user taps on the notification
        val intent = Intent(this, MainActivity::class.java).apply {
            action = ACTION_INCOMING_CALL
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            
            // Pass all call data as extras
            putExtra("call_id", callId)
            putExtra("caller_id", callerId)
            putExtra("caller_name", callerName)
            callerAvatar?.let { putExtra("caller_avatar", it) }
            putExtra("target_type", targetType)
            targetId?.let { putExtra("target_id", it) }
            targetName?.let { putExtra("target_name", it) }
            targetAvatarUrl?.let { putExtra("target_avatar_url", it) }
            sdpOffer?.let { putExtra("sdp_offer", it) }
            putExtra("video_offered", isVideoOffered)
            putExtra("audio_offered", isAudioOffered)
            putExtra("screen_share_offered", isScreenShareOffered)
            
            // Tell Flutter to show the call UI immediately
            putExtra("should_show_call_ui", true)
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // Same intent can be used for full-screen intent
        val fullScreenIntent = pendingIntent
        
        // Create a high-priority notification with ringtone and vibration
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE)
        
        val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(android.R.drawable.ic_dialog_info) // Replace with your app icon
            .setContentTitle("Incoming call from $callerName")
            .setContentText(if (targetType == "bubble") "Group call: ${targetName ?: ""}" else "Incoming call")
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_CALL)
            .setSound(defaultSoundUri)
            .setVibrate(longArrayOf(0, 1000, 500, 1000))
            .setAutoCancel(true)
            .setOngoing(true)
            .setContentIntent(pendingIntent)
            .setFullScreenIntent(fullScreenIntent, true)
        
        // Show the notification
        with(NotificationManagerCompat.from(this)) {
            // In production, handle the PERMISSION_GRANTED check 
            try {
                notify(notificationId.incrementAndGet(), notificationBuilder.build())
                Log.d(TAG, "Showed incoming call notification for call $callId")
            } catch (e: SecurityException) {
                Log.e(TAG, "No permission to show notification", e)
            }
        }
    }
    
    private fun createNotificationChannel() {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val importance = NotificationManager.IMPORTANCE_HIGH
            val channel = NotificationChannel(CHANNEL_ID, CHANNEL_NAME, importance).apply {
                description = "Channel for incoming call notifications"
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 1000, 500, 1000)
                setSound(
                    RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE),
                    null
                )
            }
            
            // Register the channel with the system
            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")
        
        // If you want to send messages to this application instance or
        // manage this apps subscriptions on the server side, send the
        // Instance ID token to your app server.
        sendRegistrationToServer(token)
    }
    
    private fun sendRegistrationToServer(token: String?) {
        // TODO: Implement this method to send token to your app server.
        Log.d(TAG, "sendRegistrationTokenToServer($token)")
    }
} 