package com.hopenapp.hopen

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import androidx.core.view.WindowCompat
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.util.Log

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.hopenapp.hopen/call"
    private var methodChannel: MethodChannel? = null
    private var broadcastReceiver: BroadcastReceiver? = null
    
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        
        // Register to receive broadcasts from the FCM service
        registerBroadcastReceiver()
        
        // Check if app was launched from a notification
        handleIncomingCallIntent(intent)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Make navigation bar transparent
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            window.isNavigationBarContrastEnforced = false
        }
        
        // Make the app draw behind system bars (edge-to-edge)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Make the status bar and navigation bar transparent
        window.setFlags(
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        )
        
        // For incoming calls, turn on screen and show even when locked
        if (intent?.action == HopenFirebaseMessagingService.ACTION_INCOMING_CALL) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                setShowWhenLocked(true)
                setTurnScreenOn(true)
            } else {
                window.addFlags(
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                )
            }
        }
        
        // Make navigation bar icons light
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.systemBarsBehavior = androidx.core.view.WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
    }
    
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        
        // Handle the incoming call if present
        handleIncomingCallIntent(intent)
    }
    
    private fun registerBroadcastReceiver() {
        broadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == HopenFirebaseMessagingService.ACTION_INCOMING_CALL) {
                    handleIncomingCallIntent(intent)
                }
            }
        }
        
        val intentFilter = IntentFilter(HopenFirebaseMessagingService.ACTION_INCOMING_CALL)
        registerReceiver(broadcastReceiver, intentFilter)
    }
    
    private fun handleIncomingCallIntent(intent: Intent?) {
        if (intent?.action == HopenFirebaseMessagingService.ACTION_INCOMING_CALL) {
            Log.d("HopenCall", "Received incoming call intent in MainActivity")
            
            // Extract all call information from the intent
            val callId = intent.getStringExtra("call_id")
            val callerId = intent.getStringExtra("caller_id")
            val callerName = intent.getStringExtra("caller_name")
            val callerAvatar = intent.getStringExtra("caller_avatar")
            val targetType = intent.getStringExtra("target_type") ?: "user"
            val targetId = intent.getStringExtra("target_id")
            val targetName = intent.getStringExtra("target_name")
            val targetAvatarUrl = intent.getStringExtra("target_avatar_url")
            val sdpOffer = intent.getStringExtra("sdp_offer")
            val isVideoOffered = intent.getBooleanExtra("video_offered", false)
            val isAudioOffered = intent.getBooleanExtra("audio_offered", true)
            val isScreenShareOffered = intent.getBooleanExtra("screen_share_offered", false)
            
            if (callId == null || callerId == null) {
                Log.e("HopenCall", "Missing required call information in intent")
                return
            }
            
            // Prepare data to send to Flutter
            val callData = HashMap<String, Any>().apply {
                put("call_id", callId)
                put("caller_id", callerId)
                put("caller_name", callerName ?: "Unknown")
                callerAvatar?.let { put("caller_avatar", it) }
                put("is_group", targetType == "bubble")
                targetId?.let { put("group_id", it) }
                targetName?.let { put("group_name", it) }
                targetAvatarUrl?.let { put("group_avatar_url", it) }
                sdpOffer?.let { put("remote_offer_sdp", it) }
                put("is_video_offered", isVideoOffered)
                put("is_audio_offered", isAudioOffered)
                put("is_screen_share_offered", isScreenShareOffered)
            }
            
            // Send data to Flutter via method channel
            methodChannel?.invokeMethod("incomingCall", callData, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d("HopenCall", "Successfully sent call data to Flutter")
                }
                
                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e("HopenCall", "Error sending call data to Flutter: $errorCode - $errorMessage")
                }
                
                override fun notImplemented() {
                    Log.e("HopenCall", "Method channel method not implemented in Flutter")
                }
            })
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        if (broadcastReceiver != null) {
            unregisterReceiver(broadcastReceiver)
            broadcastReceiver = null
        }
    }
} 