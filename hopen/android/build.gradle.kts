plugins {
    id("com.android.application") version "8.6.0" apply false
    id("org.jetbrains.kotlin.android") version "2.1.0" apply false
    id("com.google.gms.google-services") version "4.4.1" apply false
}

allprojects {
    repositories {
        google()
        mavenCentral()
        // Required for com.transistorsoft:tsbackgroundfetch used by the background_fetch plugin
        maven(url = "https://repo.transistorsoft.com/android")
        // Fallback mirror for Transistorsoft artifacts if primary repo is unreachable
        maven(url = "https://jitpack.io")
        // Local repository for background_fetch's prepackaged AAR (tsbackgroundfetch)
        maven {
            // The plugin exposes the native AAR in <plugin>/android/libs
            url = uri("${project(":background_fetch").projectDir}/libs")
        }
    }
    
    // Suppress obsolete Java version warnings globally
    tasks.withType<JavaCompile>().configureEach {
        options.compilerArgs.addAll(listOf("-Xlint:-options"))
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
