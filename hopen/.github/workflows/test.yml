name: Hopen Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  FLUTTER_VERSION: '3.16.0'
  JAVA_VERSION: '17'

jobs:
  # Job 1: Unit and Configuration Tests (Fast)
  unit-tests:
    name: Unit & Configuration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Verify Flutter installation
        run: flutter doctor -v
        
      - name: Run code generation
        run: |
          if grep -q "build_runner" pubspec.yaml; then
            flutter packages pub run build_runner build --delete-conflicting-outputs
          fi
          
      - name: Run unit tests
        run: ./scripts/run_tests.sh -g unit -e ci -c -v
        
      - name: Run configuration tests
        run: ./scripts/run_tests.sh -g config -e ci -v
        
      - name: Upload unit test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: unit-test-results
          path: test_results/
          retention-days: 7
          
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info
          flags: unit-tests
          name: unit-tests-coverage
          fail_ci_if_error: false

  # Job 2: BLoC Tests (Specialized)
  bloc-tests:
    name: BLoC State Management Tests
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Run BLoC tests
        run: ./scripts/run_tests.sh -g bloc -e ci -c -p --concurrency 4
        
      - name: Upload BLoC test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: bloc-test-results
          path: test_results/
          retention-days: 7

  # Job 3: Integration Tests (Medium)
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 25
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: hopen_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Setup test database
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client
          PGPASSWORD=test_password psql -h localhost -U test_user -d hopen_test -c "CREATE EXTENSION IF NOT EXISTS pgcrypto;"
          
      - name: Run integration tests
        run: ./scripts/run_tests.sh -g integration -e ci -c -v
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/hopen_test
          
      - name: Upload integration test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: test_results/
          retention-days: 7
          
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info
          flags: integration-tests
          name: integration-tests-coverage
          fail_ci_if_error: false

  # Job 4: End-to-End Tests (Slow)
  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    timeout-minutes: 35
    
    strategy:
      matrix:
        device: [web, android]
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Enable web support
        if: matrix.device == 'web'
        run: flutter config --enable-web
        
      - name: Setup Android emulator
        if: matrix.device == 'android'
        uses: reactivecircus/android-emulator-runner@v2
        with:
          api-level: 33
          target: google_apis
          arch: x86_64
          profile: Nexus 6
          script: echo "Emulator started"
          
      - name: Run E2E tests (Web)
        if: matrix.device == 'web'
        run: |
          flutter drive \
            --driver=test_driver/integration_test.dart \
            --target=integration_test/app_test.dart \
            -d web-server --web-port=7357
            
      - name: Run E2E tests (Android)
        if: matrix.device == 'android'
        uses: reactivecircus/android-emulator-runner@v2
        with:
          api-level: 33
          target: google_apis
          arch: x86_64
          profile: Nexus 6
          script: ./scripts/run_tests.sh -g e2e -e ci -v --timeout 1800
          
      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results-${{ matrix.device }}
          path: test_results/
          retention-days: 7

  # Job 5: Performance Tests (Optional)
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: github.event_name == 'schedule' || contains(github.event.head_commit.message, '[performance]')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Run performance tests
        run: ./scripts/run_tests.sh -g performance -e ci --performance --trend-analysis
        
      - name: Upload performance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-test-results
          path: test_results/
          retention-days: 30

  # Job 6: Test Result Analysis
  test-analysis:
    name: Test Analysis & Reporting
    runs-on: ubuntu-latest
    needs: [unit-tests, bloc-tests, integration-tests, e2e-tests]
    if: always()
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download all test artifacts
        uses: actions/download-artifact@v3
        with:
          path: all-test-results/
          
      - name: Setup Node.js for report generation
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install report dependencies
        run: |
          npm install -g junit-report-merger
          npm install -g lcov-result-merger
          
      - name: Merge test reports
        run: |
          # Merge JUnit XML reports
          find all-test-results -name "*.xml" -type f | head -10 | xargs junit-report-merger --out merged-test-results.xml || echo "No XML reports found"
          
          # Merge coverage reports
          find all-test-results -name "lcov.info" -type f | head -10 | xargs lcov-result-merger --out merged-coverage.info || echo "No coverage reports found"
          
      - name: Generate test summary
        run: |
          echo "# Test Summary" > test-summary.md
          echo "" >> test-summary.md
          echo "## Test Results by Category" >> test-summary.md
          echo "" >> test-summary.md
          
          # Count test results
          unit_tests=$(find all-test-results/unit-test-results -name "*.xml" 2>/dev/null | wc -l || echo "0")
          bloc_tests=$(find all-test-results/bloc-test-results -name "*.xml" 2>/dev/null | wc -l || echo "0")
          integration_tests=$(find all-test-results/integration-test-results -name "*.xml" 2>/dev/null | wc -l || echo "0")
          e2e_tests=$(find all-test-results -name "*e2e*" -type d | wc -l || echo "0")
          
          echo "- Unit Tests: $unit_tests reports" >> test-summary.md
          echo "- BLoC Tests: $bloc_tests reports" >> test-summary.md
          echo "- Integration Tests: $integration_tests reports" >> test-summary.md
          echo "- E2E Tests: $e2e_tests reports" >> test-summary.md
          echo "" >> test-summary.md
          
          # Add coverage summary if available
          if [ -f "merged-coverage.info" ]; then
            echo "## Coverage Summary" >> test-summary.md
            echo "Coverage report generated successfully" >> test-summary.md
          fi
          
      - name: Upload merged results
        uses: actions/upload-artifact@v3
        with:
          name: merged-test-results
          path: |
            merged-test-results.xml
            merged-coverage.info
            test-summary.md
          retention-days: 30
          
      - name: Upload final coverage to Codecov
        uses: codecov/codecov-action@v3
        if: hashFiles('merged-coverage.info') != ''
        with:
          file: merged-coverage.info
          flags: all-tests
          name: complete-test-coverage
          fail_ci_if_error: false

  # Job 7: Notification (Optional)
  notify:
    name: Test Notifications
    runs-on: ubuntu-latest
    needs: [unit-tests, bloc-tests, integration-tests, e2e-tests, test-analysis]
    if: always() && (github.event_name == 'schedule' || failure())
    
    steps:
      - name: Notify on test failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#development'
          text: |
            🚨 Test Suite Failed on ${{ github.ref }}
            
            Repository: ${{ github.repository }}
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}
            
            Check the details: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          
      - name: Notify on scheduled success
        if: success() && github.event_name == 'schedule'
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#development'
          text: |
            ✅ Daily Test Suite Passed
            
            All tests completed successfully for ${{ github.repository }}
            Latest commit: ${{ github.sha }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Job 8: Security and Quality Checks
  quality-checks:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Run Flutter analyze
        run: flutter analyze --fatal-infos
        
      - name: Run dart format check
        run: dart format --set-exit-if-changed .
        
      - name: Check for outdated dependencies
        run: flutter pub outdated --mode=null-safety
        
      - name: Run dependency validation
        run: flutter pub deps