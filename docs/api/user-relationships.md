# User Relationships API

This document describes the new relationship-based user management system that replaces the legacy ban system.

## Overview

The user relationships system allows for flexible management of various types of relationships between users, including:

- **Ban**: Administrative action to restrict user access
- **Block**: User-initiated action to prevent interaction with another user
- **Mute**: Temporary restriction of notifications or communications
- **Restrict**: Limited access restrictions
- **Follow**: User following relationships
- **Favorite**: User favorites/bookmarks

## Database Schema

### user_relationships Table

```sql
CREATE TABLE user_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    to_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_by UUID REFERENCES users(id),
    reason TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(from_user_id, to_user_id, relationship_type, status)
);
```

## API Endpoints

### 1. Get User Relationships

**GET** `/api/users/{userId}/relationships`

Retrieves all relationships for a specific user.

**Query Parameters:**
- `type` (optional): Filter by relationship type (ban, block, mute, etc.)

**Response:**
```json
{
  "user_id": "uuid",
  "relationships": [
    {
      "id": "uuid",
      "from_user_id": "uuid",
      "to_user_id": "uuid",
      "relationship_type": "ban",
      "status": "active",
      "created_by": "uuid",
      "reason": "Violation of terms",
      "metadata": {
        "severity": "high",
        "duration_days": 30
      },
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "expires_at": "2023-01-31T00:00:00Z"
    }
  ],
  "count": 1
}
```

### 2. Get Ban Details

**GET** `/api/users/{userId}/ban-details`

Retrieves detailed ban information for a user.

**Response:**
```json
{
  "user_id": "uuid",
  "ban_details": {
    "id": "uuid",
    "from_user_id": "uuid",
    "to_user_id": "uuid",
    "relationship_type": "ban",
    "status": "active",
    "created_by": "uuid",
    "reason": "Violation of terms",
    "metadata": {
      "severity": "high",
      "violation_type": "harassment"
    },
    "created_at": "2023-01-01T00:00:00Z",
    "expires_at": null
  }
}
```

### 3. Create User Relationship

**POST** `/api/users/relationships`

Creates a new user relationship.

**Request Body:**
```json
{
  "from_user_id": "uuid",
  "to_user_id": "uuid",
  "relationship_type": "ban",
  "reason": "Violation of community guidelines",
  "metadata": {
    "severity": "medium",
    "duration_days": 7
  },
  "expires_at": "2023-01-08T00:00:00Z"
}
```

**Response:**
```json
{
  "message": "Relationship created successfully",
  "relationship": {
    "id": "uuid",
    "from_user_id": "uuid",
    "to_user_id": "uuid",
    "relationship_type": "ban",
    "status": "active",
    "created_by": "uuid",
    "reason": "Violation of community guidelines",
    "metadata": {
      "severity": "medium",
      "duration_days": 7
    },
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z",
    "expires_at": "2023-01-08T00:00:00Z"
  }
}
```

### 4. Remove User Relationship

**DELETE** `/api/users/relationships/{fromUserId}/{toUserId}/{type}`

Removes (deactivates) a user relationship.

**Response:**
```json
{
  "message": "Relationship removed successfully"
}
```

### 5. Ban User (Enhanced)

**POST** `/api/users/{userId}/ban`

Bans a user with enhanced metadata support.

**Request Body:**
```json
{
  "reason": "Spam and harassment",
  "duration_days": 30,
  "metadata": {
    "severity": "high",
    "violation_type": "harassment",
    "previous_violations": 2
  }
}
```

**Response:**
```json
{
  "message": "User banned successfully",
  "ban_details": {
    "user_id": "uuid",
    "banned_by": "uuid",
    "banned_at": "2023-01-01T00:00:00Z",
    "reason": "Spam and harassment",
    "expires_at": "2023-01-31T00:00:00Z"
  }
}
```

### 6. Unban User (Enhanced)

**POST** `/api/users/{userId}/unban`

Unbans a user with audit trail.

**Response:**
```json
{
  "message": "User unbanned successfully",
  "unban_details": {
    "user_id": "uuid",
    "unbanned_by": "uuid",
    "unbanned_at": "2023-01-15T00:00:00Z",
    "original_ban": {
      "banned_at": "2023-01-01T00:00:00Z",
      "reason": "Spam and harassment",
      "banned_by": "uuid"
    }
  }
}
```

## Permissions

- **Admin-only actions**: ban, restrict, unban
- **User actions**: block, mute (only for themselves)
- **View permissions**: Users can view their own relationships, admins can view any user's relationships

## Migration

The system includes migration scripts to safely move from the legacy ban system:

1. **Migration script**: `migrations/postgresql/001_migrate_bans_to_relationships.sql`
2. **Rollback script**: `migrations/postgresql/001_rollback_bans_to_relationships.sql`

## Metadata Examples

### Ban Metadata
```json
{
  "severity": "high|medium|low",
  "duration_days": 30,
  "violation_type": "spam|harassment|inappropriate_content",
  "appeal_allowed": true,
  "previous_violations": 2
}
```

### Block Metadata
```json
{
  "block_type": "full|messages_only|calls_only",
  "auto_block": false
}
```

### Mute Metadata
```json
{
  "mute_type": "notifications|messages|calls",
  "duration_hours": 24
}
```
