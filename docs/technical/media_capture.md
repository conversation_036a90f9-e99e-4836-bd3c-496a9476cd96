# Media Capture System

## Overview

The Media Capture System in Hopen provides a modern, feature-rich camera experience for capturing photos and videos within chat conversations. The implementation leverages the `camera` package to provide a high-quality, customizable camera interface with robust features.

## Key Features

- **Photo and video capture**: Users can capture both photos and videos
- **Preview capability**: Review captured media before sending
- **Retake functionality**: Option to discard and retake the photo or video
- **Playback controls**: For video preview before sending
- **Permission handling**: Built-in camera and microphone permission requests
- **Modern UI**: Clean, intuitive interface with proper feedback
- **Flash control**: Toggle between flash modes (off, auto, always)
- **Camera switching**: Switch between front and back cameras
- **Video recording**: Start/stop video recording with visual indicators

## Architecture

```
╭─────────────────────────────────────────────────╮
│              Media Capture Architecture         │
│                                                 │
│  ╭────────────────╮       ╭────────────────╮    │
│  │  Chat Pages    │──────▶│ CameraAwesome  │    │
│  │  (UI Triggers) │       │     Page       │    │
│  ╰────────────────╯       ╰────────────────╯    │
│           │                       │             │
│           │                       │             │
│  ╭────────────────╮       ╭────────────────╮    │
│  │  Media Preview │◀──────│   Permissions  │    │
│  │     Screen     │       │    Handler     │    │
│  ╰────────────────╯       ╰────────────────╯    │
│           │                                     │
│           ▼                                     │
│  ╭────────────────╮                             │
│  │ Chat Message   │                             │
│  │ Media Display  │                             │
│  ╰────────────────╯                             │
╰─────────────────────────────────────────────────╯
```

## Implementation Details

### CameraAwesomePage

The `CameraAwesomePage` is a stateful widget that provides the core functionality of the media capture system:

1. **Initialization**:
   - Requests camera and microphone permissions
   - Initializes available cameras
   - Sets up camera controller with high resolution preset

2. **Media Capture**:
   - Handles both photo and video capture using the camera package
   - Saves media to temporary storage with unique filenames
   - Provides visual feedback during capture (recording indicator for videos)

3. **Camera Controls**:
   - Flash mode cycling (off → auto → always → off)
   - Camera switching between front and back cameras
   - Photo/video mode toggle

4. **Preview and Confirmation**:
   - Shows preview of captured photo or video
   - Plays videos with controls and progress indicator
   - Provides "Retake" and "Send" options

5. **Return Handling**:
   - Returns a `CameraResult` object containing the file and media type
   - Handles proper cleanup of resources and temporary files

### Integration with Chat

The media capture system is integrated into the chat interface through:

1. **Chat Pages**:
   - `chat_page.dart` - Main chat interface
   - `bubble_chat_page.dart` - Bubble chat interface
   
2. **Media Handling**:
   - Both chat interfaces call `CameraAwesomePage.captureMediaAndSend()`
   - Differentiate between photo and video for proper message rendering
   - Upload media to storage and send as message

## Usage Flow

1. User taps the camera icon in a chat interface
2. Permission request dialogs appear if needed
3. Camera interface opens with controls for photo/video mode, flash, and camera switching
4. User captures media (photo or records video)
5. Preview screen appears with options to accept or retake
6. Upon acceptance, media is attached to a new message
7. Message is sent with the appropriate media type

## Dependencies

- `camera`: Official Flutter camera plugin with comprehensive features
- `video_player`: Video playback functionality for preview
- `permission_handler`: Manages permission requests
- `path_provider`: Handles file paths for temporary storage

## Future Improvements

- Add support for filters and effects
- Implement multi-media capture in a single session
- Add gallery/library access for existing media
- Optimize video compression for better performance 