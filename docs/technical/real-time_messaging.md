# Real-time Messaging Implementation with MQTT 5.0

## Overview

The real-time messaging system in Hopen is built using **MQTT 5.0** with enhanced features for reliable, scalable communication:

- **Unified MQTT 5.0 Service** for real-time messaging and in-app notifications
- **EMQX MQTT 5.0 Broker** with enhanced features for message persistence and delivery
- **User Properties** for enhanced message metadata and routing
- **Session Expiry** for configurable connection persistence
- **Message Deduplication** for idempotent message processing
- **MinIO S3-compatible Storage** for high-performance file storage (media attachments)
- **Drift Local Storage** for efficient offline capabilities
- **Go 1.23 Realtime Service** (Gin) backed by **Cassandra** for chat history
- **NATS JetStream** for event propagation between services
- **Valkey Caching** for real-time session management
- **Structured Logging** using dart:developer for comprehensive debugging and monitoring
- **Firebase Cloud Messaging (FCM)** for push notifications when the app is backgrounded

### Performance Benefits with MQTT 5.0

- **Enhanced message delivery**: User properties for intelligent routing
- **Session persistence**: Configurable session expiry for mobile optimization
- **Message deduplication**: Prevents duplicate processing and improves reliability
- **Reason codes**: Detailed error information for better debugging
- **Quality of Service**: Guaranteed message delivery with QoS levels
- **Connection resilience**: Automatic reconnection with exponential backoff

## Architecture

```
╭─────────────────────────────────────────────────────────────╮
│                   Client Architecture                       │
│                                                             │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │  Chat UI    │   │  Chat BLoC  │   │    Chat     │        │
│  │  (Pages)    │──▶│             │──▶│ Repository  │        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
│         │                                    │              │
│         │                                    │              │
│  ╭─────────────╮                     ╭─────────────╮        │
│  │   Message   │                     │  Services   │        │
│  │  Components │                     │             │        │
│  ╰─────────────╯                     ╰─────────────╯        │
│                                           │                 │
│                                      ╭─────────────╮        │
│                                      │MQTT 5.0     │        │
│                                      │MinIO        │        │
│                                      │Drift        │        │
│                                      │NATS         │        │
│                                      ╰─────────────╯        │
╰─────────────────────────────────────────────────────────────╯
```

## Backend Architecture (simplified)

```
╭─────────────────────────────────────────────────────────────╮
│                Go Realtime Service Backend                  │
│                                                             │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │ API Gateway │   │ Realtime    │   │ Notification│        │
│  │             │──▶│Service      │──▶│Service (SES)│        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
│         │                 │                 │               │
│         │                 │                 │               │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │   Security  │   │  Message    │   │   MQTT 5.0  │        │
│  │ Middleware  │   │ Processing  │   │   Handler   │        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
│                                           │                 │
│                                      ╭─────────────╮        │
│                                      │   EMQX      │        │
│                                      │MQTT 5.0     │        │
│                                      │  Broker     │        │
│                                      ╰─────────────╯        │
╰─────────────────────────────────────────────────────────────╯
```

## Real-time Communication Flow with MQTT 5.0

```
╭──────────────────────────────────────────────────────────────────╮
│                 MQTT 5.0 Enhanced Messaging Flow                 │
│                                                                  │
│  ╭─────────────╮         ╭─────────────╮         ╭─────────────╮ │
│  │   Client A  │         │ EMQX MQTT   │         │   Client B  │ │
│  │             │         │5.0 Broker   │         │             │ │
│  │ ╭─────────╮ │ MQTT5   │             │ MQTT5   │ ╭─────────╮ │ │
│  │ │Publisher│ ├────────▶│   Enhanced  │────────▶│ │Subscrbr │ │ │
│  │ ╰─────────╯ │ Props   │   Topics    │ Props   │ ╰─────────╯ │ │
│  │             │         │ /bubble/123 │         │             │ │
│  │ ╭─────────╮ │         │ /user/456   │         │ ╭─────────╮ │ │
│  │ │Subscrbr │ │◀────────│ Persistence │◀────────│ │Publisher│ │ │
│  │ ╰─────────╯ │ Reason  │ Session Exp │ Reason  │ ╰─────────╯ │ │
│  ╰─────────────╯ Codes   │ User Props  │ Codes   ╰─────────────╯ │
│                          │ Dedup Logic │                         │
│                          ╰─────────────╯                         │
│                                │                                 │
│                                ▼                                 │
│                         ╭─────────────╮                          │
│                         │      Go     │                          │
│                         │   Backend   │                          │
│                         │ ╭─────────╮ │                          │
│                         │ │Chat Svc │ │                          │
│                         │ │MQTT 5.0 │ │                          │
│                         │ ╰─────────╯ │                          │
│                         │ ╭─────────╮ │                          │
│                         │ │PostgreSQL││                          │
│                         │ │& Valkey │ │                          │
│                         │ ╰─────────╯ │                          │
│                         ╰─────────────╯                          │
╰──────────────────────────────────────────────────────────────────╯
```

### MQTT 5.0 Enhanced Features

- **User Properties**: Custom key-value pairs for enhanced message metadata
- **Session Expiry**: Configurable session persistence (default: 1 hour)
- **Reason Codes**: Detailed error information for connection and subscription failures
- **Message Deduplication**: Prevents duplicate message processing using correlation data
- **Enhanced Authentication**: JWT-based authentication with EMQX API Auth
- **Quality of Service**: Reliable message delivery with QoS 0, 1, and 2 support

## Components

### 1. Unified MQTT 5.0 Service

The core `MqttService` provides comprehensive MQTT 5.0 functionality:

**Key Features:**
- **Persistent connections** with automatic reconnection and exponential backoff
- **Quality of Service (QoS)** levels for reliable message delivery
- **Topic-based messaging** for bubble/group communications
- **Message persistence** for offline scenarios
- **In-app notification** delivery with user properties
- **Real-time presence** and typing indicators
- **Connection status** monitoring and handling
- **Message acknowledgments** and delivery confirmations
- **Message deduplication** using correlation data for idempotency

**Enhanced MQTT 5.0 Implementation:**
```dart
class MqttService {
  // MQTT 5.0 enhanced features
  static const int _sessionExpiryInterval = 3600; // 1 hour
  static const int _keepAliveInterval = 60;       // 60 seconds
  
  // Message deduplication and idempotency
  final Set<String> _processedMessageIds = {};
  final Map<String, DateTime> _messageTimestamps = {};
  
  // Enhanced publish with MQTT 5.0 user properties
  Future<void> publishWithProperties(
    String topic, 
    String message, 
    Map<String, String> userProperties, {
    MqttQos qos = MqttQos.atLeastOnce,
    bool retain = false,
  });
  
  // Subscribe with MQTT 5.0 enhanced features
  Future<bool> subscribe(
    String topic, {
    MqttQos qos = MqttQos.atLeastOnce,
    Map<String, String>? userProperties,
  });
}
```

**Topic Structure:**
- `/bubble/{bubbleId}/messages` - Bubble chat messages
- `/bubble/{bubbleId}/typing` - Typing indicators
- `/bubble/{bubbleId}/presence` - Member presence status
- `/user/{userId}/notifications` - Direct user notifications
- `/user/{userId}/status` - User online/offline status
- `/call/{callId}/signaling` - WebRTC signaling messages
- `/call/{callId}/participants` - Call participant updates

### 2. Realtime Service (Go + Cassandra)

The **realtime** micro-service (`hopenbackend/microservices/realtime/`) is responsible for chat message storage, retrieval and integration with EMQX:

**Key Components:**
* `handler.go` – Gin HTTP handlers (`/api/v1/realtime/...`).
* `store_cassandra.go` – high-throughput writes using [gocql](https://github.com/gocql/gocql).
* `publisher.go` – publishes chat events on NATS (`events.chat.sent`).

**Send-message flow:**
```go
// POST /api/v1/realtime/bubbles/:id/messages
func (h *Handler) SendMessage(c *gin.Context) {
    bubbleID := c.Param("id")

    var req struct {
        Content      string `json:"content" binding:"required,max=5000"`
        MessageType  string `json:"message_type" binding:"required,oneof=text image video audio file"`
        MediaURL     string `json:"media_url,omitempty"`
        ReplyToID    string `json:"reply_to_id,omitempty"`
    }
    if err := c.ShouldBindJSON(&req); err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    msgID, err := h.store.SaveMessage(c.Request.Context(), bubbleID, c.GetString("user_id"), req)
    if err != nil {
        c.AbortWithError(http.StatusInternalServerError, err)
        return
    }

    // Fan-out via MQTT topic
    h.mqtt.Publish(fmt.Sprintf("/bubble/%s/messages", bubbleID), marshalMQTT(msgID, req))

    // Publish event on NATS for analytics / notifications pipeline
    h.nats.Publish("events.chat.sent", marshalEvent(msgID, bubbleID))

    c.JSON(http.StatusCreated, gin.H{"message_id": msgID})
}
```

### 3. Storage Service (`provider/services/storage/storage_service.dart`)

- **MinIO S3-compatible storage** for user-generated content
- **Presigned URL generation** for secure, time-limited file access
- **File lifecycle management** (upload, download, delete)
- **Support for multiple file types** (`File` objects and `Uint8List` data)
- **Bucket management** and initialization
- **Progress tracking** for large file uploads

### 4. Messaging Service (`provider/services/messaging_service.dart`)

- **MQTT client coordination** for real-time messaging
- **Firebase Cloud Messaging (FCM)** for push notifications (background only)
- **Topic subscription management** for bubble memberships
- **Message routing** between MQTT and local storage
- **Connection resilience** with automatic reconnection strategies
- **Message queuing** for offline scenarios

### 5. Drift Local Storage

- **Efficient local message storage** using Drift (SQLite wrapper)
- **Offline-first architecture** with automatic synchronization
- **Complex query support** for message history and search
- **Type-safe database operations** with compile-time validation
- **Migration support** for schema updates
- **Transaction support** for data consistency

### 6. REST API Integration (Gin)

- **Type-safe API client** with automatic serialization
- **Authentication token management** with interceptors
- **Error handling** with structured exception classes
- **Retry mechanisms** for failed requests
- **Caching strategies** for API responses
- **Circuit breaker patterns** for resilience

## Message Flow

### 1. Sending a Message

```
User Input → UI → BLoC → Repository → Realtime REST API (Gin)
                              ├─→ Drift (local storage)
                              ├─→ MinIO (if has attachments)
                              ├─→ Cassandra (persistence)
                              ├─→ Valkey (caching)
                              └─→ MQTT 5.0 Publish (with user properties)
```

### 2. Receiving a Message

```
MQTT 5.0 Subscribe → Repository → BLoC → UI Update
                           ├─→ Drift (local storage)
                           ├─→ Cassandra (background sync)
                           └─→ FCM (if app backgrounded)
```

### 3. File Attachment Flow

```
File Selection → MinIO Upload → Presigned URL → Message with URL
                      ├─→ Progress Tracking
                      ├─→ Error Handling
                      └─→ Cleanup on Failure
```

### 4. Offline Message Handling

```
Send Attempt → Network Check → Queue Locally → Background Sync
                     ├─→ Drift Storage
                     ├─→ Retry Logic
                     └─→ Conflict Resolution
```

## MQTT 5.0 Authentication

### JWT-based Authentication

The MQTT 5.0 broker uses JWT tokens for authentication:

```go
// MQTT5AuthRequest for enhanced authentication
type MQTT5AuthRequest struct {
    ClientID    string            `json:"clientid"`
    Username    string            `json:"username"`
    Password    string            `json:"password"`  // JWT token
    Properties  map[string]string `json:"properties,omitempty"` // MQTT 5.0 user properties
}

// MQTT5AuthResponse with enhanced features
type MQTT5AuthResponse struct {
    IsAuthenticated bool              `json:"is_authenticated"`
    UserID          string            `json:"user_id,omitempty"`
    ACL             *ACL              `json:"acl,omitempty"`
    Properties      map[string]string `json:"properties,omitempty"` // MQTT 5.0 response properties
    SessionExpiry   int64             `json:"session_expiry,omitempty"` // MQTT 5.0 session expiry
}
```

### Enhanced ACL with MQTT 5.0

```go
// ACL with MQTT 5.0 enhancements
type ACL struct {
    Pub    []string          `json:"pub,omitempty"`
    Sub    []string          `json:"sub,omitempty"`
    Pubsub []string          `json:"pubsub,omitempty"` // Combined pub/sub permissions
    Props  map[string]string `json:"props,omitempty"`  // MQTT 5.0 ACL properties
}

// Generate user-specific ACL for MQTT 5.0
func generateMQTT5ACL(userID string) *ACL {
    return &ACL{
        Pub: []string{
            fmt.Sprintf("user/%s/status", userID),
            "bubble/+/messages",
            "bubble/+/typing",
        },
        Sub: []string{
            fmt.Sprintf("user/%s/notifications", userID),
            "bubble/+/messages",
            "bubble/+/presence",
        },
        Props: map[string]string{
            "user_id":      userID,
            "mqtt_version": "5.0",
            "acl_version":  "1.0",
        },
    }
}
```

## Error Handling & Resilience

### Connection Management

```dart
class MqttService {
  // Enhanced reconnection with exponential backoff
  Future<void> _scheduleReconnection() async {
    if (!_shouldReconnect || _reconnectAttempts >= _maxReconnectAttempts) {
      return;
    }

    final delay = _baseReconnectDelay * pow(2, _reconnectAttempts);
    final maxDelay = Duration(minutes: 5);
    final actualDelay = delay > maxDelay ? maxDelay : delay;

    _reconnectTimer = Timer(actualDelay, () async {
      _reconnectAttempts++;
      await _establishConnection();
    });
  }

  // Message deduplication
  bool _isMessageProcessed(String messageId) {
    _cleanupOldMessageIds();
    return _processedMessageIds.contains(messageId);
  }

  void _markMessageProcessed(String messageId) {
    _processedMessageIds.add(messageId);
    _messageTimestamps[messageId] = DateTime.now();
  }
}
```

### Error Recovery Patterns

- **Automatic Reconnection**: Exponential backoff with maximum retry limits
- **Message Queuing**: Local storage for offline scenarios
- **Duplicate Detection**: Correlation data prevents duplicate processing
- **Circuit Breaker**: Prevents cascading failures
- **Graceful Degradation**: Fallback to cached data when network unavailable

## Performance Optimizations

### Message Optimization

- **Compression**: Message payload compression for large messages
- **Batching**: Multiple small messages combined for efficiency
- **Prioritization**: High-priority messages (calls) get precedence
- **Cleanup**: Automatic cleanup of old processed message IDs

### Connection Optimization

- **Keep-Alive Tuning**: Optimized for mobile network conditions
- **Session Persistence**: Reduces connection overhead
- **Topic Optimization**: Efficient topic subscription patterns
- **QoS Selection**: Appropriate QoS levels for different message types

### Monitoring & Metrics

- **Connection Health**: Real-time connection status monitoring
- **Message Delivery**: Tracking delivery success rates
- **Performance Metrics**: Latency and throughput measurements
- **Error Tracking**: Comprehensive error logging and analysis

This MQTT 5.0 implementation provides a robust, scalable foundation for real-time messaging in the Hopen application, with enhanced features for reliability, performance, and user experience.

## Implementation Details

### MQTT Message Protocol

Messages follow a structured JSON format optimized for mobile clients:

```json
{
  "id": "msg_uuid_v4",
  "type": "text|image|file|system",
  "bubble_id": "bubble_uuid",
  "sender_id": "user_uuid",
  "content": "message content",
  "timestamp": "2024-01-15T10:30:00Z",
  "attachments": [
    {
      "id": "file_uuid",
      "type": "image/jpeg",
      "url": "presigned_url",
      "size": 1048576,
      "name": "image.jpg"
    }
  ],
  "metadata": {
    "reply_to": "msg_uuid",
    "edited": false,
    "delivery_status": "sent|delivered|read"
  }
}
```

### MQTT Topic Strategy

**Hierarchical Topic Structure:**
- **Root**: `/hopen/v1/`
- **Bubble Messages**: `/hopen/v1/bubble/{bubbleId}/messages`
- **Bubble Events**: `/hopen/v1/bubble/{bubbleId}/events`
- **User Notifications**: `/hopen/v1/user/{userId}/notifications`
- **System Broadcasts**: `/hopen/v1/system/announcements`

**QoS Levels:**
- **QoS 0**: Typing indicators, presence updates (fire and forget)
- **QoS 1**: Chat messages, notifications (at least once)
- **QoS 2**: Critical system messages (exactly once)

### File Upload Process

1. **Client-side preparation**:
   - Generate unique file ID
   - Validate file type and size
   - Compress image/video if needed

2. **MinIO upload**:
   - Request presigned upload URL from Realtime REST API
   - Upload file directly to MinIO with progress tracking
   - Verify upload completion

3. **Message creation**:
   - Create message with file metadata
   - Publish to MQTT topic
   - Store in local Drift database

### Offline Handling Strategy

1. **Message queuing**:
   - Store outgoing messages in local queue
   - Mark with pending status
   - Retry on reconnection

2. **State synchronization**:
   - Compare local and remote state
   - Resolve conflicts using timestamp priority
   - Update UI with final state

3. **Graceful degradation**:
   - Display cached content when offline
   - Show connection status to user
   - Queue user actions for later processing

### Security Implementation

- **MQTT authentication** using JWT tokens from Ory Stack
- **Topic-level authorization** based on bubble membership
- **File access control** via presigned URLs with expiration
- **Message encryption** (planned for future versions)
- **Rate limiting** on MQTT topics
- **Input validation** and sanitization

## Usage Examples

### Sending a Message with MQTT

```dart
// Via Chat Repository
final message = await chatRepository.sendMessage(
  bubbleId: 'bubble_123',
  content: 'Hello everyone!',
  attachments: [
    MessageAttachment(
      type: AttachmentType.image,
      path: 'file://path/to/image.jpg',
    )
  ],
  replyTo: 'msg_456', // Optional reply
);
```

### Subscribing to Bubble Messages

```dart
// Listen to real-time messages via MQTT
chatRepository.getBubbleMessagesStream(bubbleId: 'bubble_123')
  .listen((message) {
    // Handle incoming message
    print('New message: ${message.content}');
});
```

### File Upload with Progress

```dart
// Upload file to MinIO with progress tracking
final uploadStream = storageService.uploadFileWithProgress(
  file: selectedFile,
  onProgress: (progress) {
    print('Upload progress: ${(progress * 100).toInt()}%');
  },
);

final fileUrl = await uploadStream.last;
```

### Managing MQTT Connection

```dart
// Check connection status
final isConnected = await messagingService.isConnected();

// Manual reconnection
if (!isConnected) {
  await messagingService.reconnect();
}

// Listen to connection status
messagingService.connectionStatusStream.listen((status) {
  print('MQTT connection: $status');
});
```

### Offline Message Handling

```dart
// Get cached messages when offline
final cachedMessages = await chatRepository.getCachedMessages(
  bubbleId: 'bubble_123',
  limit: 50,
);

// Sync when back online
await chatRepository.syncOfflineMessages();
```

This MQTT-based implementation provides superior reliability, persistence, and real-time performance compared to traditional WebSocket approaches, while maintaining a clean architecture that supports offline-first functionality.

## Error Handling

1. **Connection Issues**
   - Automatic reconnection
   - Exponential backoff
   - User feedback
   - Message queue preservation

2. **File Upload Failures**
   - Retry mechanism
   - Progress preservation
   - Error reporting
   - Cleanup of partial uploads

3. **Storage Issues**
   - Quota management
   - Cache cleanup
   - Error recovery
   - Data integrity checks

## Testing

1. **Unit Tests**
   - Service method testing
   - Message handling
   - Error scenarios
   - State management

2. **Integration Tests**
   - End-to-end message flow
   - File upload process
   - Offline behavior
   - Notification delivery

3. **Performance Tests**
   - Message throughput
   - Connection handling
   - Memory usage
   - Battery impact

## Monitoring

- WebSocket connection status
- Message delivery rates
- File upload success rates
- Storage usage
- Error rates
- Client performance metrics

## Future Improvements

1. End-to-end encryption
2. Message editing
3. Rich text formatting
4. Voice messages
5. Video messages
6. Message scheduling
7. Custom notification sounds
8. Advanced search capabilities
