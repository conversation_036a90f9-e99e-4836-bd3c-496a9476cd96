# Notifications System Technical Documentation

## System Architecture

╭───────────────────────────────────────────────────────╮
│                 Notifications System                  │
│                                                       │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮  │
│  │  MQTT       │   │    Drift    │   │     UI      │  │
│  │ Real-time   │   │   Storage   │   │  Components │  │
│  ╰──────┬──────╯   ╰──────┬──────╯   ╰─────┬───────╯  │
│         │                 │                │          │
│         ╰─────────────────┴────────────────╯          │
╰───────────────────────────┬───────────────────────────╯
                            │
╭───────────────────────────▼───────────────────────────╮
│                    Core Components                    │
│                                                       │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮  │
│  │Notification │   │ BLoC State  │   │ Repository  │  │
│  │   Factory   │   │ Management  │   │  Interface  │  │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯  │
╰───────────────────────────────────────────────────────╯

## Backend Notification Architecture

╭─────────────────────────────────────────────────────────────╮
│              Go Notification Backend                        │
│                                                             │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │ API Gateway │   │Notification │   │   EMQX      │        │
│  │             │──▶│Microservice │──▶│   MQTT      │        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
│         │                 │                 │               │
│         │                 │                 │               │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │   Security  │   │  Event      │   │   AWS SES   │        │
│  │ Middleware  │   │ Processing  │   │   Email     │        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
│                                           │                 │
│                                    ╭─────────────╮          │
│                                    │   Valkey    │          │
│                                    │   Cache     │          │
│                                    ╰─────────────╯          │
╰─────────────────────────────────────────────────────────────╯

## Real-time Notification Flow

╭─────────────────────────────────────────────────────────────╮
│              MQTT-based Notification Delivery              │
│                                                             │
│  ╭─────────────╮         ╭─────────────╮         ╭─────────────╮
│  │  Go         │  MQTT   │ EMQX MQTT   │  MQTT   │   Flutter   │
│  │   Backend   │ Publish │   Broker    │Subscribe│     App     │
│  │             ├────────▶│             ├────────▶│             │
│  │ ╭─────────╮ │         │ /user/{id}/ │         │ ╭─────────╮ │
│  │ │Event    │ │         │notifications│         │ │In-App   │ │
│  │ │Triggers │ │         │ Persistence │         │ │Handler  │ │
│  │ ╰─────────╯ │         │ & Delivery  │         │ ╰─────────╯ │
│  ╰─────────────╯         │             │         ╰─────────────╯
│                          ╰─────────────╯                        │
│                                │                               │
│                                ▼                               │
│                         ╭─────────────╮                        │
│                         │  AWS SES    │                        │
│                         │ Email Only  │                        │
│                         ╰─────────────╯                        │
╰─────────────────────────────────────────────────────────────────╯

## Data Flow Architecture

╭──────────────╮    ╭───────────────────╮   ╭─────────────╮
│MQTT Message  │───▶│NotificationFactory│──▶│Repository   │
│(Real-time)   │    │Creates Model      │   │Stores Data  │
╰──────────────╯    ╰───────────────────╯   ╰───┬─────────╯
                                                │
╭──────────────╮    ╭────────────────╮    ╭─────▼──────╮
│UI Components │◀───│NotificationBloc│◀───│Drift Stream│
│Show Updates  │    │Updates State   │    │Events      │
╰──────────────╯    ╰────────────────╯    ╰────────────╯

## Notification Categories Flow

╭────────────────────────────────────────────────╮
│            Smart Notification Strategy         │
│                                                │
│  ╭──────────╮    ╭──────────╮    ╭──────────╮  │
│  │ Dialog   │    │Notification│   │System    │  │
│  │ Events   │    │   List     │   │Updates   │  │
│  ╰────┬─────╯    ╰────┬─────╯    ╰────┬─────╯  │
│       │               │               │        │
│       ▼               ▼               ▼        │
│  ╭──────────╮    ╭──────────╮    ╭──────────╮  │
│  │Immediate │    │Background│    │Background│  │
│  │ Dialogs  │    │Notifications│  │Notifications│
│  ╰──────────╯    ╰──────────╯    ╰──────────╯  │
╰────────────────────────────────────────────────╯

## Dialog vs Notification Strategy

╭─────────────────────────────────────────────────────────────╮
│                   Event Handling Strategy                   │
│                                                             │
│  ╭─────────────╮                    ╭─────────────╮         │
│  │   Events    │                    │   Events    │         │
│  │ Requiring   │ ──────────────────▶│ For Info    │         │
│  │ Immediate   │                    │ & History   │         │
│  │  Action     │                    │             │         │
│  ╰─────────────╯                    ╰─────────────╯         │
│        │                                   │                │
│        ▼                                   ▼                │
│  ╭─────────────╮                    ╭─────────────╮         │
│  │   Show      │                    │   Add to    │         │
│  │  Dialog     │                    │Notification │         │
│  │             │                    │    List     │         │
│  ╰─────────────╯                    ╰─────────────╯         │
╰─────────────────────────────────────────────────────────────╯

## Technology Stack Integration

╭─────────────────────────────────────────────────────────────╮
│                    Technology Components                    │
│                                                             │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │ MQTT Client │   │   Drift     │   │  Go.        │        │
│  │(In-app only)│   │ (Storage)   │   │  Backend    │        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
│                                                             │
│  ╭─────────────╮   ╭─────────────╮   ╭─────────────╮        │
│  │ Generated   │   │   API       │   │ PostHog     │        │
│  │   API       │   │             │   │ Analytics   │        │
│  ╰─────────────╯   ╰─────────────╯   ╰─────────────╯        │
╰─────────────────────────────────────────────────────────────╯

## Notification Microservice (Go + AWS SES)

The notification microservice (`hopenbackend/microservices/notification/`) handles all notification-related operations:

**Key Components:**
- `notification.go`: Core notification processing and delivery
- `email.go`: Email notification handling via AWS SES
- `mqtt.go`: MQTT notification publishing

**Core Notification Operations:**
```go
// Send notification endpoint
func (s *Service) SendNotification(ctx context.Context, req *SendNotificationRequest) (*SendNotificationResponse, error) {
    // Validate user permissions
    if !s.validateNotificationPermissions(ctx, req.TargetUserID) {
        return nil, status.Error(codes.PermissionDenied, "permission denied")
    }
    
    // Create notification entity
    notification := &Notification{
        ID:          generateNotificationID(),
        UserID:      req.TargetUserID,
        Category:    req.Category,
        Message:     req.Message,
        Description: req.Description,
        Priority:    req.Priority,
        Payload:     req.Payload,
        CreatedAt:   time.Now(),
        IsRead:      false,
    }
    
    // Store in PostgreSQL
    err := s.db.StoreNotification(ctx, notification)
    if err != nil {
        return nil, err
    }
    
    // Send email notification (only channel) for priority notifications
    if notification.Priority == NotificationPriority_High {
        err = s.email.SendEmailNotification(ctx, notification)
        if err != nil {
            // Log error but don't fail the request
            log.Error("Failed to send email notification", err)
        }
    }
    
    return &SendNotificationResponse{
        NotificationID: notification.ID,
        DeliveredAt:    notification.CreatedAt,
    }, nil
}

// Mark notification as read
func (s *Service) MarkAsRead(ctx context.Context, req *MarkAsReadRequest) (*MarkAsReadResponse, error) {
    userID := auth.UserID()
    if userID == "" {
        return nil, status.Error(codes.Unauthenticated, "unauthenticated")
    }
    
    // Update notification status
    err := s.db.MarkNotificationAsRead(ctx, req.NotificationID, userID)
    if err != nil {
        return nil, err
    }
    
    // Publish read status update via MQTT
    err = s.mqtt.PublishReadStatus(ctx, req.NotificationID, userID)
    if err != nil {
        log.Error("Failed to publish read status", err)
    }
    
    return &MarkAsReadResponse{
        Success: true,
        UpdatedAt: time.Now(),
    }, nil
}
```

## Data Persistence Strategy

**Three-Tier Storage Architecture:**

1. **PostgreSQL (Primary Storage)**
   - Persistent notification records
   - User notification preferences
   - Notification history and analytics
   - Sharded by user_id for scalability

2. **Valkey (Cache Layer)**
   - Recent notifications (last 24 hours)
   - User online status
   - Notification delivery status
   - Real-time counters and metrics

3. **Drift (Local Storage)**
   - Offline notification queue
   - User preferences cache
   - Local notification history
   - Sync state management

## Overview

The Notifications system in Hopen provides real-time updates about user interactions, contact relationships, and bubble activities. This system has been redesigned to use **MQTT for in-app real-time notifications** and **Firebase Cloud Messaging (FCM) only for background push notifications** when the app is not active. **Flutter local notifications have been removed** in favor of a cleaner, MQTT-based approach.

## Smart Notification Strategy

The system implements a **smart dual-approach** to prevent notification spam and provide optimal user experience:

### **Dialog-Based Events** 🎯
Events requiring **immediate user action** show dedicated dialogs:
- **Contact requests** → `ContactRequestDialog`
- **Bubble invitations** → `BubbleInviteRequestDialog`
- **Bubble join requests** → `BubbleJoinRequestDialog`
- **New bubble members** → `BubbleMemberJoinedDialog`
- **Bubble votekick requests** → `BubbleKickoutRequestDialog`
- **New friendships** → `FriendshipEstablishedDialog`

### **Notification List Events** 📋
Events for **information and history** appear in the notifications list:
- **Response notifications** (accepted/declined requests)
- **Communication notifications** (messages, calls)
- **Lifecycle notifications** (bubble expiration reminders)
- **System notifications** (security alerts, app updates)

### **Key Benefits** ✅
- **No duplicate notifications** for the same event
- **Clear separation** between actions and information
- **Immediate attention** for important decisions
- **Clean notification history** for reference

## Core Technology Components

### 1. MQTT Real-time Notifications
- **EMQX MQTT Broker** for reliable message delivery
- **Topic-based subscriptions** for user-specific notifications (`/user/{userId}/notifications`)
- **Quality of Service (QoS) 1** for guaranteed delivery
- **Persistent connections** with automatic reconnection
- **In-app only** - no local notification popups

### 2. Firebase Cloud Messaging (FCM)
- **Background push notifications only** when app is backgrounded/terminated
- **Rich notifications** with custom payloads
- **Silent notifications** for background sync
- **Platform-specific handling** (iOS/Android)

### 3. Drift Local Storage
- **Type-safe notification storage** using Drift database
- **Offline access** to notification history
- **Automatic synchronization** with real-time updates
- **Complex queries** for filtering and searching

### 4. Service Call Flow
- **Notification event triggers** from backend services
- **User activity tracking** and notification scheduling
- **Bubble lifecycle notifications** (creation, expiration, etc.)
- **Contact request workflows** with automated notifications

## Core Components

1. **`Notification` Model (`lib/statefulbusinesslogic/core/models/notification.dart`)**

    * **Purpose:** Represents a single notification instance with enhanced metadata.
    * **Key Properties:**

        ```dart
        class Notification extends Equatable {
          final String id; // Unique identifier
          final NotificationCategory category; // Type of notification (enum)
          final String message; // User-facing alert text
          final String description; // Internal description of the event
          final bool isRead; // Read status
          final DateTime timestamp; // Time the notification occurred
          final Map<String, dynamic>? payload; // Optional data for context/navigation
          final String? sourceUserId; // ID of user who triggered the notification
          final String? bubbleId; // Related bubble ID if applicable
          final NotificationPriority priority; // High, medium, low priority
        }
        ```

    * **Categories (`NotificationCategory` enum):**

## Dialog-Based Events (Not in Notification List)

These events show **dedicated dialogs** and do NOT appear in the notification list to avoid duplicates:

### Contact/Friend Requests (Dialog-Based)
1. **contactRequestReceived** ❌
   - **Dialog**: `ContactRequestDialog`
   - **Purpose**: When a user receives a contact request from another user
   - **Action**: User can accept or decline the request
   - **Location**: `/lib/presentation/widgets/requests/contact_request_dialog.dart`

2. **contactRequestAccepted** ❌
   - **Dialog**: `ContactshipEstablishedDialog`
   - **Purpose**: When someone accepts the user's contact request
   - **Action**: User acknowledges the new contactship with "OK" button
   - **Location**: `/lib/presentation/widgets/requests/contactship_established_dialog.dart`

3. **friendshipEstablished** ❌
   - **Dialog**: `FriendshipEstablishedDialog`
   - **Purpose**: When another user accepts the viewing user's friend request (after FriendsChoiceDialog completion)
   - **Action**: User acknowledges the new friendship with "OK" button
   - **Location**: `/lib/presentation/widgets/requests/friendship_established_dialog.dart`

### Bubble Interactions (Dialog-Based)
4. **bubbleInvitationReceived** ❌
   - **Dialog**: `BubbleInviteRequestDialog`
   - **Purpose**: When a user receives an invitation to join a bubble
   - **Action**: User can accept or decline the invitation
   - **Location**: `/lib/presentation/widgets/requests/bubble_invite_request_dialog.dart`

5. **bubbleJoinRequestReceived** ❌
   - **Dialog**: `BubbleJoinRequestDialog`
   - **Purpose**: When someone requests to join a bubble the user manages
   - **Action**: User can accept or decline the request (requires unanimous approval)
   - **Location**: `/lib/presentation/widgets/requests/bubble_join_request_dialog.dart`

6. **bubbleJoinRequestAccepted** ❌
   - **Dialog**: `BubbleMemberJoinedDialog`
   - **Purpose**: When a user's request to join a bubble is accepted
   - **Action**: User acknowledges the new member with "OK" button
   - **Location**: `/lib/presentation/widgets/requests/bubble_member_joined_dialog.dart`

7. **bubbleMemberJoined** ❌
   - **Dialog**: `BubbleMemberJoinedDialog`
   - **Purpose**: When a new member joins a bubble the user is in
   - **Action**: User acknowledges the new member with "OK" button
   - **Location**: `/lib/presentation/widgets/requests/bubble_member_joined_dialog.dart`

8. **bubbleVotekickInitiated** ❌
   - **Dialog**: `BubbleKickoutRequestDialog`
   - **Purpose**: When a vote to kick a member from a bubble begins
   - **Action**: User can vote to approve or reject the kickout
   - **Location**: `/lib/presentation/widgets/requests/bubble_kickout_request_dialog.dart`

## Notification List Events

These events appear in the **notification list** for information and history:

### Contact/Friend Requests (Notification List)
1. **contactRequestDeclined** ✅
   - **Purpose**: Notifies when someone declines the user's contact request
   - **Display**: Shows the profile picture of the user who declined (falls back to `Icons.person_remove` icon)
   - **Handling**: No special handling when tapped, just marks as read
   - **Payload**: Requires `userId` for profile picture display

### Bubble Invitations & Join Requests (Notification List)
2. **bubbleJoinRequestRejected** ✅
   - **Purpose**: Notifies when a user's request to join a bubble is rejected
   - **Display**: Uses custom image `assets/images/3d/200px/normal/thumb-down.png` (falls back to `Icons.block` icon)
   - **Handling**: No special handling when tapped, just marks as read
   - **Multi-User**: Shown to both the requester AND all bubble members
   - **Note**: This notification is sent after any bubble member rejects the request

3. **bubbleInviteRequestRejected** ✅
   - **Purpose**: Notifies when a user's invitation to join a bubble is declined
   - **Display**: Uses custom image `assets/images/3d/200px/normal/thumb-down.png` (falls back to `Icons.block` icon)
   - **Handling**: No special handling when tapped, just marks as read
   - **Multi-User**: Shown to both the invitee AND all bubble members
   - **Note**: This notification is sent when the invitee declines the invitation

### Bubble Management & Interaction (Notification List)
4. **bubbleVotekickPassed** ✅
    - **Purpose**: Notifies when a vote to kick a member from a bubble succeeds
    - **Display**: Uses custom image `assets/images/3d/200px/normal/flash.png` (falls back to `Icons.person_off` icon)
    - **Handling**: No special handling when tapped, just marks as read
    - **Note**: This is an outcome notification, informing users that the votekick was successful

### Bubble Messages & Calls (Notification List)
5. **bubbleChatMessageReceived** ✅
    - **Purpose**: Notifies when a new message is received in a bubble chat
    - **Display**: Uses custom image `assets/images/3d/200px/normal/chat.png` (falls back to `Icons.chat_bubble` icon)
    - **Handling**: No special handling when tapped, just marks as read

6. **bubbleVoiceMessageReceived** ✅
    - **Purpose**: Notifies when a voice message is received in a bubble
    - **Display**: Uses custom image `assets/images/3d/200px/normal/mic.png` (falls back to `Icons.mic_none` icon)
    - **Handling**: No special handling when tapped, just marks as read

7. **bubbleVideoMessageReceived** ✅
    - **Purpose**: Notifies when a video message is received in a bubble
    - **Display**: Uses custom image `assets/images/3d/200px/normal/video-cam.png` (falls back to `Icons.videocam` icon)
    - **Handling**: No special handling when tapped, just marks as read

8. **bubbleAudioCallIncoming** ✅
    - **Purpose**: Notifies when there's an incoming audio call in a bubble
    - **Display**: Uses custom image `assets/images/3d/200px/normal/call-in.png` (falls back to `Icons.add_ic_call` icon)
    - **Handling**: No special handling when tapped, just marks as read

9. **bubbleVideoCallIncoming** ✅
    - **Purpose**: Notifies when there's an incoming video call in a bubble
    - **Display**: Uses custom image `assets/images/3d/200px/normal/video-cam.png` (falls back to `Icons.video_call` icon)
    - **Handling**: No special handling when tapped, just marks as read

10. **bubbleScreenShareIncoming** ✅
    - **Purpose**: Notifies when someone starts screen sharing in a bubble
    - **Display**: Uses custom image `assets/images/3d/200px/normal/mobile.png` (falls back to `Icons.screen_share` icon)
    - **Handling**: No special handling when tapped, just marks as read

11. **bubbleCallInProgress** ✅
    - **Purpose**: Notifies when a call is ongoing in a bubble
    - **Display**: Uses custom image `assets/images/3d/200px/normal/call-ringing.png` (falls back to `Icons.phone_in_talk` icon)
    - **Handling**: No special handling when tapped, just marks as read

12. **bubbleCallEnded** ✅
    - **Purpose**: Notifies when a call in a bubble has ended
    - **Display**: Uses custom image `assets/images/3d/200px/normal/call-end.png` (falls back to `Icons.call_end` icon)
    - **Handling**: No special handling when tapped, just marks as read

13. **bubbleMissedCall** ✅
    - **Purpose**: Notifies when the user missed a call in a bubble
    - **Display**: Uses custom image `assets/images/3d/200px/normal/chat.png` (falls back to `Icons.phone_missed` icon)
    - **Handling**: No special handling when tapped, just marks as read

### Bubble Lifecycle (Notification List)
14. **bubblePopReminder60Days** ✅
    - **Purpose**: Reminds that a bubble will pop in 60 days
    - **Display**: Uses custom image `assets/images/3d/200px/normal/calender.png` (falls back to `Icons.hourglass_empty` icon)
    - **Handling**: No special handling when tapped, just marks as read

15. **bubblePopReminder30Days** ✅
    - **Purpose**: Reminds that a bubble will pop in 30 days
    - **Display**: Uses custom image `assets/images/3d/200px/normal/calender.png` (falls back to `Icons.hourglass_empty` icon)
    - **Handling**: No special handling when tapped, just marks as read

16. **bubblePopReminder20Days** ✅
    - **Purpose**: Reminds that a bubble will pop in 20 days
    - **Display**: Uses custom image `assets/images/3d/200px/normal/calender.png` (falls back to `Icons.hourglass_empty` icon)
    - **Handling**: No special handling when tapped, just marks as read

17. **bubblePopReminder10Days** ✅
    - **Purpose**: Reminds that a bubble will pop in 10 days
    - **Display**: Uses custom image `assets/images/3d/200px/normal/calender.png` (falls back to `Icons.hourglass_empty` icon)
    - **Handling**: No special handling when tapped, just marks as read

18. **bubblePopReminder7Days** ✅
    - **Purpose**: Reminds that a bubble will pop in 7 days
    - **Display**: Uses custom image `assets/images/3d/200px/normal/calender.png` (falls back to `Icons.hourglass_empty` icon)
    - **Handling**: No special handling when tapped, just marks as read

19. **bubblePopReminder3Days** ✅
    - **Purpose**: Reminds that a bubble will pop in 3 days
    - **Display**: Uses custom image `assets/images/3d/200px/normal/calender.png` (falls back to `Icons.hourglass_empty` icon)
    - **Handling**: No special handling when tapped, just marks as read

20. **bubblePopReminder24Hours** ✅
    - **Purpose**: Reminds that a bubble will pop in 24 hours
    - **Display**: Uses custom image `assets/images/3d/200px/normal/calender.png` (falls back to `Icons.hourglass_empty` icon)
    - **Handling**: No special handling when tapped, just marks as read

21. **bubblePopped** ✅
    - **Purpose**: Notifies when a bubble has popped/expired
    - **Display**: Uses custom image `assets/images/3d/200px/normal/bubble-pop.png` (falls back to `Icons.bubble_chart` icon)
    - **Handling**: No special handling when tapped, just marks as read
    - **Note**: This triggers the FriendsChoiceDialog for selecting friends from bubble members

### Direct Friend Interactions (Notification List)
22. **friendChatMessageReceived** ✅
    - **Purpose**: Notifies when a direct message is received from a friend
    - **Display**: Shows the profile picture of the sender (falls back to `Icons.message` icon)
    - **Handling**: No special handling when tapped, just marks as read
    - **Payload**: Requires `senderId` for profile picture display

23. **friendVoiceMessageReceived** ✅
    - **Purpose**: Notifies when a voice message is received from a friend
    - **Display**: Shows the profile picture of the sender (falls back to `Icons.spatial_audio_off` icon)
    - **Handling**: No special handling when tapped, just marks as read
    - **Payload**: Requires `senderId` for profile picture display

24. **friendVideoMessageReceived** ✅
    - **Purpose**: Notifies when a video message is received from a friend
    - **Display**: Shows the profile picture of the sender (falls back to `Icons.personal_video` icon)
    - **Handling**: No special handling when tapped, just marks as read
    - **Payload**: Requires `senderId` for profile picture display

25. **friendAudioCallIncoming** ✅
    - **Purpose**: Notifies when there's an incoming audio call from a friend
    - **Display**: Shows the profile picture of the caller (falls back to `Icons.call` icon)
    - **Handling**: No special handling when tapped, just marks as read
    - **Payload**: Requires `callerId` for profile picture display

26. **friendVideoCallIncoming** ✅
    - **Purpose**: Notifies when there's an incoming video call from a friend
    - **Display**: Shows the profile picture of the caller (falls back to `Icons.videocam` icon)
    - **Handling**: No special handling when tapped, just marks as read
    - **Payload**: Requires `callerId` for profile picture display

27. **friendScreenShareIncoming** ✅
    - **Purpose**: Notifies when a friend starts screen sharing
    - **Display**: Shows the profile picture of the sharer (falls back to `Icons.screen_share` icon)
    - **Handling**: No special handling when tapped, just marks as read
    - **Payload**: Requires `callerId` for profile picture display

28. **friendMissedCall** ✅
    - **Purpose**: Notifies when the user missed a call from a friend
    - **Display**: Shows the profile picture of the caller (falls back to `Icons.call_missed_outgoing` icon)
    - **Handling**: No special handling when tapped, just marks as read
    - **Payload**: Requires `callerId` for profile picture display

29. **friendCallInProgress** ✅
    - **Purpose**: Notifies when a call with a friend is ongoing
    - **Display**: Shows the profile picture of the caller (falls back to `Icons.phone_in_talk` icon)
    - **Handling**: No special handling when tapped, just marks as read
    - **Payload**: Requires `callerId` for profile picture display

### User Activity & Engagement (Notification List)
30. **inactiveNoBubble12Hours** ✅
    - **Purpose**: Reminds inactive users without a bubble after 12 hours
    - **Display**: Uses custom image `assets/images/3d/200px/normal/notify-heart.png` (falls back to `Icons.self_improvement` icon)
    - **Handling**: No special handling when tapped, just marks as read

31. **inactiveNoBubble1Day** ✅
    - **Purpose**: Reminds inactive users without a bubble after 1 day
    - **Display**: Uses custom image `assets/images/3d/200px/normal/notify-heart.png` (falls back to `Icons.self_improvement` icon)
    - **Handling**: No special handling when tapped, just marks as read

32. **inactiveNoBubble2Days** ✅
    - **Purpose**: Reminds inactive users without a bubble after 2 days
    - **Display**: Uses custom image `assets/images/3d/200px/normal/notify-heart.png` (falls back to `Icons.self_improvement` icon)
    - **Handling**: No special handling when tapped, just marks as read

33. **inactiveNoBubble3Days** ✅
    - **Purpose**: Reminds inactive users without a bubble after 3 days
    - **Display**: Uses custom image `assets/images/3d/200px/normal/notify-heart.png` (falls back to `Icons.self_improvement` icon)
    - **Handling**: No special handling when tapped, just marks as read

34. **inactiveNoBubble7Days** ✅
    - **Purpose**: Reminds inactive users without a bubble after 7 days
    - **Display**: Uses custom image `assets/images/3d/200px/normal/notify-heart.png` (falls back to `Icons.self_improvement` icon)
    - **Handling**: No special handling when tapped, just marks as read

### General Categories (Notification List)
35. **statusUpdates** ✅
    - **Purpose**: General status updates about the app or user account
    - **Display**: Uses custom image `assets/images/3d/200px/normal/tick.png` (falls back to `Icons.update` icon)
    - **Handling**: No special handling when tapped, just marks as read

36. **securityAlerts** ✅
    - **Purpose**: Security-related notifications (login attempts, password changes)
    - **Display**: Uses custom image `assets/images/3d/200px/normal/shield.png` (falls back to `Icons.security` icon)
    - **Handling**: No special handling when tapped, just marks as read

37. **appUpdates** ✅
    - **Purpose**: Notifies about app updates or new features
    - **Display**: Uses custom image `assets/images/3d/200px/normal/rocket.png` (falls back to `Icons.system_update` icon)
    - **Handling**: No special handling when tapped, just marks as read

## Summary of Notification Strategy

### **Total Dialog-Based Events**: 8 types ❌
- All require immediate user action/acknowledgment
- Show dedicated dialogs with action buttons
- Do NOT appear in notification list to avoid duplicates

### **Total Notification List Events**: 37 types ✅
- Informational updates and responses
- Communication notifications (messages, calls)
- Lifecycle reminders and system updates
- Appear in notification list for user reference

### **Key Benefits** 🎯
- **No notification spam**: Clear separation between actions and information
- **Immediate attention**: Important decisions get dedicated dialogs
- **Clean history**: Notification list contains only relevant information
- **Better UX**: Users aren't overwhelmed with duplicate notifications

## Database Schema (PostgreSQL)

The notifications system uses an enterprise-grade PostgreSQL schema with the following improvements:

### **Schema Structure**
```sql
-- Notification type ENUM for type safety
CREATE TYPE notification_type AS ENUM (
    -- Contact/Friend Requests (notification list only)
    'contactRequestDeclined',

    -- Bubble Invitations & Join Requests (notification list only)
    'bubbleJoinRequestRejected',
    'bubbleInviteRequestRejected',

    -- Bubble Management & Interaction (notification list only)
    'bubbleVotekickPassed',

    -- All communication, lifecycle, and system notifications...
    -- (37 total types for notification list)
);

-- Notifications table with enterprise-grade design
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    -- CRITICAL: UUID foreign key with cascade deletion for data integrity
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    -- Type-safe ENUM prevents invalid notification types
    type notification_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- CRITICAL: Performance index for notification feeds
-- Optimized for: "Get unread notifications for user, newest first"
CREATE INDEX idx_notifications_user_id_is_read
ON notifications (user_id, is_read, created_at DESC);
```

### **Key Schema Benefits** 🚀
- **100x-1000x faster** notification queries with optimized composite indexes
- **Data integrity** with UUID foreign keys and cascade deletion
- **Type safety** with ENUM preventing invalid notification types
- **Privacy compliance** with automatic cleanup when users delete accounts
- **Scalable architecture** ready for millions of concurrent users
- **Notification aggregation** with grouping keys to prevent spam
- **User preference enforcement** to prevent unwanted notifications

### **Dialog-Based Exclusion Strategy** 🎯
The schema **excludes** 8 notification types that show dedicated dialogs:
- `contactRequestReceived`, `contactRequestAccepted`, `bubbleInvitationReceived`
- `bubbleJoinRequestReceived`, `bubbleJoinRequestAccepted`, `bubbleMemberJoined`
- `bubbleVotekickInitiated`, `friendshipEstablished`

This prevents duplicate notifications and creates a clean user experience.

## Enterprise-Grade Features

### **Tier 2 Best Practices** ✅ **Implemented**

#### **1. User Preference Enforcement**
The notification service now checks user preferences before creating notifications:

```go
// Before creating any notification, check user settings
func (s *Service) checkUserPreferences(ctx context.Context, userID, notificationType string) (bool, error) {
    // Check if in-app notifications are disabled
    // Check if specific notification type is disabled
    // Return true to skip notification creation
}
```

**Benefits**:
- **Prevents database bloat** by not storing unwanted notifications
- **Respects user choices** and improves user experience
- **Reduces system load** by avoiding unnecessary processing

#### **2. Optimized Performance Indexes**
Multiple indexes for different query patterns:

```sql
-- General purpose: "Get all notifications for user, newest first"
CREATE INDEX idx_notifications_user_id_created_at
ON notifications (user_id, created_at DESC);

-- Filtered queries: "Get unread notifications for user, newest first"
CREATE INDEX idx_notifications_user_id_is_read_created_at
ON notifications (user_id, is_read, created_at DESC);

-- Grouping queries: For notification aggregation
CREATE INDEX idx_notifications_grouping_key
ON notifications(user_id, grouping_key) WHERE grouping_key IS NOT NULL;
```

### **Tier 3 Advanced Features** ✅ **Implemented**

#### **1. Notification Aggregation**
Prevents notification spam by grouping similar notifications:

```go
// Example: Multiple people joining the same bubble
groupingKey := fmt.Sprintf("bubble_join:%s", bubbleID)

// First notification: "Alice joined MyBubble"
// Second notification: "Bob joined MyBubble"
// Result: "Alice, Bob and 1 other joined MyBubble"
```

**Grouping Strategies**:
- **Bubble joins**: `bubble_join:bubble_id`
- **Friend messages**: `friend_messages:sender_id`
- **Bubble messages**: `bubble_messages:bubble_id`

**Database Constraint**:
```sql
-- Ensures only one unread grouped notification per user per grouping key
CONSTRAINT unique_unread_grouped_notifications
UNIQUE (user_id, grouping_key)
WHERE is_read = false AND grouping_key IS NOT NULL
```

#### **2. Smart Update Logic**
When a grouped notification already exists:
- **Updates the message** with new information
- **Increments count** in the data JSON
- **Updates timestamp** to keep it fresh
- **Sends real-time update** via MQTT

### **Production-Ready Architecture** 🏗️

The notification system now follows enterprise-grade patterns:

1. **Separation of Concerns**: Dialog events vs notification list events
2. **Performance Optimization**: Multiple specialized indexes
3. **User Experience**: Preference enforcement and aggregation
4. **Scalability**: Efficient database design for millions of users
5. **Data Integrity**: Foreign key constraints and cascade deletion
6. **Type Safety**: ENUM constraints prevent invalid data

2. **`NotificationRepository` (`lib/repositories/notification_repository.dart`)**

    * **Purpose:** Abstracts the data source for notifications. Defines the contract for fetching, updating, and deleting notifications.
    * **Interface Methods:** `getNotifications()`, `getUnreadCount()`, `markAsRead()`, `markAllAsRead()`, `deleteNotification()`, `deleteAllNotifications()`, `notificationsStream()`, `unreadCountStream()`.
    * **Implementation (`NotificationRepositoryImpl`):** Currently uses in-memory storage with streams for real-time updates. Designed to be replaced with a concrete implementation using a backend API or local database.

3. **`NotificationBloc` (`lib/statefulbusinesslogic/bloc/notification/notification_bloc.dart`)**

    * **Purpose:** Manages the state of notifications and handles user interactions related to them. Connects the UI (Presentation layer) to the `NotificationRepository`.
    * **States:** `NotificationInitial`, `NotificationLoading`, `NotificationsLoaded`, `NotificationError`.
    * **Events:** `FetchNotifications`, `FetchUnreadCount`, `NotificationReceived`, `MarkNotificationAsRead`, `MarkAllNotificationsAsRead`, `DeleteNotification`, `DeleteAllNotifications`.
    * **Logic:** Listens to repository streams for data changes, processes UI events, and emits new states to update the UI.

4. **`NotificationFactory` (`lib/statefulbusinesslogic/bloc/notification/notification_factory.dart`)**

    * **Purpose:** Provides static methods to easily create standardized `Notification` objects for various application events based on the types defined in the initial requirements. Ensures consistency in notification messages and payloads.

## Notification Types (Examples from Factory)

This section details the specific notification types generated by the `NotificationFactory`, aligning with the initial requirements image.

| Category                             | Enum Value (`NotificationCategory`) | Factory Method Example (Illustrative)       | Notification Alert Example (User-facing)                                       | Description (Internal Purpose)                                                                    |
| :----------------------------------- | :------------------------------------ | :------------------------------------------ | :----------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------ |
| **Contact Management**               |                                       |                                             |                                                                                |                                                                                                   |
| User asks to be contact            | `contactRequestReceived`              | `createContactRequestReceived`              | "[User] wants to connect with you."                                            | Notifies when another user sends a contact request.                                               |
| User accepts contact request         | `contactRequestAccepted`              | `createContactRequestAccepted`              | "You and [User] are now connected!"                                            | Alerts when your contact request is accepted by another user.                                     |
| User declines contact request        | `contactRequestDeclined`              | `createContactRequestDeclined`              | "[User] declined your contact request."                                        | Alerts when your contact request is declined by another user.                                     |
| **Bubble Invitations & Requests**    |                                       |                                             |                                                                                |                                                                                                   |
| User invites to bubble             | `bubbleInvitationReceived`            | `createBubbleInvitationReceived`            | "[User] invited you to join their bubble '[BubbleName]'."                       | Notifies when invited to join another's bubble.                                                   |
| User requests to join bubble       | `bubbleJoinRequestReceived`           | `createBubbleJoinRequestReceived`           | "[User] wants to join your bubble '[BubbleName]'."                               | Notifies when someone wants to join your bubble. Opens BubbleJoinRequestDialog when tapped.       |
| Join request accepted (unanimous)  | `bubbleJoinRequestAccepted`           | `createBubbleJoinRequestAccepted`           | "Your request to join '[BubbleName]' was accepted!"                             | Alerts when your bubble join request is accepted by all members of the bubble.                    |
| Join request rejected (by a member)| `bubbleJoinRequestRejected`           | `createBubbleJoinRequestRejected`           | "Your request to join '[BubbleName]' was rejected by a member."                 | Alerts when your bubble join request is rejected.                                                 |
| **Bubble Membership & Activity**   |                                       |                                             |                                                                                |                                                                                                   |
| User joins your bubble             | `bubbleMemberJoined`                  | `createBubbleMemberJoined`                  | "[User] joined your bubble '[BubbleName]'." (Will also state "The bubble is now full!" if applicable) | Notifies when a new member joins your bubble. Includes info if the bubble reaches max capacity.      |
| Votekick initiated                 | `bubbleVotekickInitiated`             | `createBubbleVotekickInitiated`             | "A votekick for [TargetUser] has started in '[BubbleName]'."                    | Notifies when a votekick is initiated against another member in the bubble.                     |
| Member votekicked (unanimous)      | `bubbleVotekickPassed`                | `createBubbleVotekickPassed`                | "[TargetUser] has been removed from '[BubbleName]' by unanimous vote."          | Notifies when a member is unanimously votekicked from the bubble.                               |
| **Bubble Communication**             |                                       |                                             |                                                                                |                                                                                                   |
| Written message in bubble          | `bubbleChatMessageReceived`           | `createBubbleChatMessage`                   | "New message in '[BubbleName]' from [User]."                                    | Notifies for new written messages in a bubble chat.                                               |
| Voice message in bubble            | `bubbleVoiceMessageReceived`          | `createBubbleVoiceMessage`                  | "New voice message in '[BubbleName]' from [User]."                               | Notifies for new voice messages in a bubble chat.                                                 |
| Video message in bubble            | `bubbleVideoMessageReceived`          | `createBubbleVideoMessage`                  | "New video message in '[BubbleName]' from [User]."                               | Notifies for new video messages in a bubble chat.                                                 |
| Audio call to bubble               | `bubbleAudioCallIncoming`             | `createBubbleAudioCallIncoming`             | "[User] is starting an audio call in '[BubbleName]'."                           | Alerts for an incoming audio call to the bubble.                                                  |
| Video call to bubble               | `bubbleVideoCallIncoming`             | `createBubbleVideoCallIncoming`             | "[User] is starting a video call in '[BubbleName]'."                            | Alerts for an incoming video call to the bubble.                                                  |
| Screen share to bubble             | `bubbleScreenShareIncoming`           | `createBubbleScreenShareIncoming`           | "[User] wants to share their screen in '[BubbleName]'."                         | Alerts for an incoming screen share to the bubble.                                                |
| Call ongoing in bubble             | `bubbleCallInProgress`                | `createBubbleCallInProgress`                | "Call with [UserA, UserB] in progress in '[BubbleName]'."                     | Indicates an audio/video/screen-sharing call is ongoing (participants in payload).                |
| Call ended in bubble               | `bubbleCallEnded`                     | `createBubbleCallEnded`                     | "Call in '[BubbleName]' ended. Duration: [Duration]."                           | Notifies when a call has ended (date, time, duration in payload).                                 |
| Missed call in bubble              | `bubbleMissedCall`                    | `createBubbleMissedCall`                    | "You missed a call in '[BubbleName]' on [Date] at [Time]."                      | Notifies about a call that occurred while the user was not connected (date, time in payload).    |
| **Bubble Lifecycle**                 |                                       |                                             |                                                                                |                                                                                                   |
| Pop reminder < 60 days             | `bubblePopReminder60Days`             | `createBubblePopReminder60`                 | "Your bubble '[BubbleName]' will pop in less than 60 days!"                     | Reminder that a bubble is nearing its pop date.                                                   |
| Pop reminder < 30 days             | `bubblePopReminder30Days`             | `createBubblePopReminder30`                 | "Your bubble '[BubbleName]' will pop in less than 30 days!"                     | Reminder that a bubble is nearing its pop date.                                                   |
| Pop reminder < 20 days             | `bubblePopReminder20Days`             | `createBubblePopReminder20`                 | "Your bubble '[BubbleName]' is popping in less than 20 days!"                   | Reminder that a bubble is nearing its pop date.                                                   |
| Pop reminder < 10 days             | `bubblePopReminder10Days`             | `createBubblePopReminder10`                 | "Heads up! '[BubbleName]' pops in less than 10 days."                           | Reminder that a bubble is nearing its pop date.                                                   |
| Pop reminder < 7 days              | `bubblePopReminder7Days`              | `createBubblePopReminder7`                  | "'[BubbleName]' pops in less than 7 days. Make the most of it!"                | Reminder that a bubble is nearing its pop date.                                                   |
| Pop reminder < 3 days              | `bubblePopReminder3Days`              | `createBubblePopReminder3`                  | "Only 3 days left before '[BubbleName]' pops!"                                  | Reminder that a bubble is nearing its pop date.                                                   |
| Pop reminder < 24 hours            | `bubblePopReminder24Hours`            | `createBubblePopReminder24Hours`            | "'[BubbleName]' is popping in less than 24 hours!"                              | Final reminder before a bubble pops.                                                              |
| Bubble bursts (pops)               | `bubblePopped`                        | `createBubblePopped`                        | "Oh no! Your bubble '[BubbleName]' has popped."                                 | Notification that a bubble has reached its end and burst.                                         |
| **Direct Friend Communication**      |                                       |                                             |                                                                                |                                                                                                   |
| Friend sends written message       | `friendChatMessageReceived`           | `createFriendChatMessage`                   | "New message from [FriendName]."                                                | Alerts for new direct written messages from a friend.                                             |
| Friend sends voice message         | `friendVoiceMessageReceived`          | `createFriendVoiceMessage`                  | "[FriendName] sent you a voice message."                                        | Alerts for new direct voice messages from a friend.                                               |
| Friend sends video message         | `friendVideoMessageReceived`          | `createFriendVideoMessage`                  | "[FriendName] sent you a video message."                                        | Alerts for new direct video messages from a friend.                                               |
| Friend audio calls                 | `friendAudioCallIncoming`             | `createFriendAudioCallIncoming`             | "[FriendName] is audio calling you."                                            | Alerts for an incoming audio call from a friend.                                                  |
| Friend video calls                 | `friendVideoCallIncoming`             | `createFriendVideoCallIncoming`             | "[FriendName] is video calling you."                                            | Alerts for an incoming video call from a friend.                                                  |
| Friend screen shares               | `friendScreenShareIncoming`           | `createFriendScreenShareIncoming`           | "[FriendName] wants to share their screen with you."                            | Alerts for an incoming screen share from a friend.                                                |
| Missed call from friend            | `friendMissedCall`                    | `createFriendMissedCall`                    | "You missed a call from [FriendName]."                                          | Notifies when an audio, video, or screen-sharing call from a friend is missed.                   |
| Friend call ongoing                | `friendCallInProgress`                | `createFriendCallInProgress`                | "[FriendName] is in an ongoing call."                                           | Alerts user to an ongoing direct call with a friend they are not yet connected to.             |
| **User Engagement/Inactivity**     |                                       |                                             |                                                                                |                                                                                                   |
| 12 hours without bubble            | `inactiveNoBubble12Hours`             | `createInactiveNoBubble12h`                 | "Feeling a bit quiet? Find a new bubble to join!"                              | Reminder after 12 hours of not being in a bubble.                                                 |
| 1 day without bubble               | `inactiveNoBubble1Day`                | `createInactiveNoBubble1d`                  | "It's been a day! Time to dive into a new bubble?"                              | Reminder after 1 day of not being in a bubble.                                                    |
| 2 days without bubble              | `inactiveNoBubble2Days`               | `createInactiveNoBubble2d`                  | "Missing out on bubble fun? Join or create one!"                                | Reminder after 2 days of not being in a bubble.                                                   |
| 3 days without bubble              | `inactiveNoBubble3Days`               | `createInactiveNoBubble3d`                  | "Your next great conversation is a bubble away!"                                | Reminder after 3 days of not being in a bubble.                                                   |
| 7 days without bubble              | `inactiveNoBubble7Days`               | `createInactiveNoBubble7d`                  | "Reconnect and share moments in a Hopen bubble!"                                | Reminder after 7 days of not being in a bubble.                                                   |
| **General & System**               |                                       |                                             |                                                                                |                                                                                                   |
| Status Updates                     | `statusUpdates`                       | *(Factory method TBD)*                      | "Your invitation to [User] is no longer valid..."                              | Notifies when an invitation/request becomes invalid due to status change.                         |
| Mentions (Optional)                | `mentions`                            | *(Factory method TBD)*                      | "You were mentioned by [User] in [Bubble Name]."                               | Alerts when mentioned in a bubble message (if supported).                                         |
| Security (Optional)                | `securityAlerts`                      | *(Factory method TBD)*                      | "Your account was accessed from a new device."                                 | Notifies for potential security events (if supported).                                            |
| App Updates (Optional)             | `appUpdates`                          | *(Factory method TBD)*                      | "A new version of Hopen is available."                                         | Alerts for app updates (if supported).                                                            |

*(Note: Factory methods for some types are illustrative and would need to be implemented in `NotificationFactory` as these features evolve. The old categories like `contactRequests`, `messages`, `calls` are now covered by more specific types.)*

## UI Components

### `NotificationsDialog` (`lib/presentation/widgets/notifications_dialog.dart`)

- **Purpose:** Displays the list of notifications to the user in a modal dialog.
- **Key Features:**
  1. Integrates with `NotificationBloc` to display notifications based on the current state (`NotificationsLoaded`, `NotificationLoading`, `NotificationError`).
  2. Shows an empty state message when there are no notifications.
  3. Renders a list of `_NotificationTile` widgets for each notification.
  4. Provides a "Mark all read" button that dispatches `MarkAllNotificationsAsRead` event to the Bloc.
  5. Provides a "Close" button.
  6. Initiates `FetchNotifications` event when the dialog is opened.
  7. Uses responsive text sizing based on screen width.
  8. Employs a standard background dimming effect (`barrierColor` with 0.8 opacity) when shown.

### Request Dialog Widgets

The following dialog widgets are used to handle various types of requests in the app. For detailed documentation on these dialogs, see [Dialog Widgets Documentation](dialog_widgets.md).

#### `ContactRequestDialog` (`lib/presentation/widgets/requests/contact_request_dialog.dart`)
- **Purpose:** Appears when another user sends a request to become a contact with the current user.
- **Triggered by:** `contactRequestReceived` notification type.

#### `BubbleStartRequestDialog` (`lib/presentation/widgets/requests/bubble_start_request_dialog.dart`)
- **Purpose:** Appears when another contact sends a request to the current user, asking them to start a new bubble together.
- **Triggered by:** Custom notification type (to be implemented).

#### `BubbleJoinRequestDialog` (`lib/presentation/widgets/requests/bubble_join_request_dialog.dart`)
- **Purpose:** Appears when another user requests to join a bubble that the current user is a member of.
- **Triggered by:** `bubbleJoinRequestReceived` notification type.

#### `BubbleInviteRequestDialog` (`lib/presentation/widgets/requests/bubble_invite_request_dialog.dart`)
- **Purpose:** Appears when another contact invites the current user to join their bubble.
- **Triggered by:** `bubbleInvitationReceived` notification type.

### `_NotificationTile` (Internal to `NotificationsDialog`)

- **Purpose:** Renders a single notification item within the dialog list.
- **Key Features:**
  1. Displays the notification message and formatted timestamp (`_formatTimestamp`).
  2. Shows an icon corresponding to the `NotificationCategory` (`_getIconForCategory`).
  3. Visually distinguishes between read and unread notifications (icon background, text weight, unread indicator dot).
  4. Handles tap actions: Dispatches `MarkNotificationAsRead` event if the notification is unread. Can be extended to navigate based on `payload`.
  5. **Trailing Actions:** Includes a `Row` with `IconButton`s for direct actions:
      * **Mark as Unread** (`Icons.mark_email_unread_outlined`): Visible only when the notification `isRead` is true. Dispatches `MarkNotificationAsUnread` event.
      * **Delete** (`Icons.delete_outline`): Always visible. Dispatches `DeleteNotification` event.

## Notification Icon Implementation

The notification system uses a sophisticated approach to display icons for different notification types, prioritizing user profile pictures for user-related notifications and custom image assets for other notification types.

### Icon Selection Logic

The icon selection process follows this hierarchy:
1. For user-related notifications, attempt to display the user's profile picture
2. For other notifications, display a custom image asset
3. If neither is available or fails to load, fall back to a default Material icon

### Profile Pictures for User-Related Notifications

The following notification categories display the profile picture of the relevant user **in the notification list**:

- **Contact/Friend Requests** (Notification List Only):
  - `contactRequestDeclined`: Shows the profile picture of the user who declined

- **Friend Communications** (Notification List Only):
  - `friendChatMessageReceived`: Shows the profile picture of the sender
  - `friendVoiceMessageReceived`: Shows the profile picture of the sender
  - `friendVideoMessageReceived`: Shows the profile picture of the sender
  - `friendAudioCallIncoming`: Shows the profile picture of the caller
  - `friendVideoCallIncoming`: Shows the profile picture of the caller
  - `friendScreenShareIncoming`: Shows the profile picture of the sharer
  - `friendMissedCall`: Shows the profile picture of the caller
  - `friendCallInProgress`: Shows the profile picture of the caller

### ❌ **Dialog-Based Types (No Longer in Notification List)**

The following types now show **dedicated dialogs** and do NOT appear in the notification list:
- `contactRequestReceived` → Shows `ContactRequestDialog`
- `contactRequestAccepted` → Shows `ContactshipEstablishedDialog`
- `friendshipEstablished` → Shows `FriendshipEstablishedDialog`
- `bubbleInvitationReceived` → Shows `BubbleInviteRequestDialog`
- `bubbleJoinRequestReceived` → Shows `BubbleJoinRequestDialog`
- `bubbleMemberJoined` → Shows `BubbleMemberJoinedDialog`

### Custom Image Assets for Notification List Types

The following notification categories use custom image assets **in the notification list**:

- **Bubble Join Requests** (Notification List Only):
  - `bubbleJoinRequestRejected`: Uses `assets/images/3d/200px/normal/thumb-down.png`
  - `bubbleInviteRequestRejected`: Uses `assets/images/3d/200px/normal/thumb-down.png`

- **Bubble Management** (Notification List Only):
  - `bubbleVotekickPassed`: Uses `assets/images/3d/200px/normal/flash.png`

### ❌ **Dialog-Based Types (No Longer Use Custom Images in Notification List)**
- `bubbleJoinRequestAccepted` → Now shows `BubbleMemberJoinedDialog`
- `bubbleVotekickInitiated` → Now shows `BubbleKickoutRequestDialog`

- **Bubble Communications**:
  - `bubbleChatMessageReceived`: Uses `assets/images/3d/200px/normal/chat.png`
  - `bubbleVoiceMessageReceived`: Uses `assets/images/3d/200px/normal/mic.png`
  - `bubbleVideoMessageReceived`: Uses `assets/images/3d/200px/normal/video-cam.png`
  - `bubbleAudioCallIncoming`: Uses `assets/images/3d/200px/normal/call-in.png`
  - `bubbleVideoCallIncoming`: Uses `assets/images/3d/200px/normal/video-cam.png`
  - `bubbleScreenShareIncoming`: Uses `assets/images/3d/200px/normal/mobile.png`
  - `bubbleCallInProgress`: Uses `assets/images/3d/200px/normal/call-ringing.png`
  - `bubbleCallEnded`: Uses `assets/images/3d/200px/normal/call-end.png`
  - `bubbleMissedCall`: Uses `assets/images/3d/200px/normal/chat.png`

- **Bubble Lifecycle** (Notification List Only):
  - All `bubblePopReminder*` categories: Use `assets/images/3d/200px/normal/calender.png`
  - `bubblePopped`: Uses `assets/images/3d/200px/normal/bubble-pop.png`

- **User Engagement** (Notification List Only):
  - `inactiveNoBubble12Hours`, `inactiveNoBubble1Day`, `inactiveNoBubble2Days`, `inactiveNoBubble3Days`, `inactiveNoBubble7Days`: Use `assets/images/3d/200px/normal/notify-heart.png`

- **General Categories** (Notification List Only):
  - `statusUpdates`: Uses `assets/images/3d/200px/normal/tick.png`
  - `securityAlerts`: Uses `assets/images/3d/200px/normal/shield.png`
  - `appUpdates`: Uses `assets/images/3d/200px/normal/rocket.png`

### Fallback Mechanisms

The system includes robust fallback mechanisms:

1. **For Profile Pictures**:
   - If the user ID is not found in the payload, falls back to custom image or default icon
   - If the user has no profile picture, displays the first letter of their name in a circle avatar
   - If there's an error fetching user info, falls back to custom image or default icon

2. **For Custom Images**:
   - If the image asset fails to load, falls back to the default Material icon
   - Logs the error for debugging purposes

## New Dialog Components

The following dialog components have been created to handle immediate user interactions:

### **ContactshipEstablishedDialog**
- **Location**: `/lib/presentation/widgets/requests/contactship_established_dialog.dart`
- **Purpose**: Shows when someone accepts the user's contact request
- **Triggered by**: `contactRequestAccepted` events
- **Features**:
  - Displays contact's profile picture and name
  - Shows "[Name] accepted your contact request!" message
  - Shows acceptance timestamp ("Accepted 5m ago")
  - Single "OK" button for acknowledgment
  - Animated gradient title: "New contact!"
  - Same design pattern as other request dialogs
  - Non-dismissible (requires user acknowledgment)

### **BubbleMemberJoinedDialog**
- **Location**: `/lib/presentation/widgets/requests/bubble_member_joined_dialog.dart`
- **Purpose**: Shows when someone joins the user's bubble
- **Triggered by**: `bubbleJoinRequestAccepted` and `bubbleMemberJoined` events
- **Features**:
  - Displays new member's profile picture and name
  - Shows bubble name and join timestamp ("Joined 5m ago")
  - Single "OK" button for acknowledgment
  - Animated gradient title: "New bubble member"
  - Same design pattern as other request dialogs
  - Non-dismissible (requires user acknowledgment)

### **FriendshipEstablishedDialog**
- **Location**: `/lib/presentation/widgets/requests/friendship_established_dialog.dart`
- **Purpose**: Shows when another user accepts the viewing user's friend request
- **Context**: Triggered when viewing user completed FriendsChoiceDialog first, and other user has now also completed their choice
- **Triggered by**: `friendshipEstablished` events
- **Features**:
  - Displays new friend's profile picture and name
  - Shows "You and [Name] are now friends!" message
  - Shows friendship establishment timestamp
  - Single "OK" button for acknowledgment
  - Animated gradient title: "New friendship!"
  - Same design pattern as other request dialogs
  - Non-dismissible (requires user acknowledgment)

### **Dialog Design Consistency** 🎨
All dialogs follow the same design pattern:
- **Animated gradient titles** with color transitions
- **Profile picture display** with fallback to first letter avatar
- **Responsive sizing** based on screen dimensions
- **Non-dismissible behavior** requiring user acknowledgment
- **Consistent button styling** with blue "OK" buttons
- **Timestamp formatting** (just now, 5m ago, 2h ago, etc.)

### **Removed Notification Types**

The following notification types are filtered out and not displayed in the notification list:

- **Dialog-based types** (8 total): Show dedicated dialogs instead of notification list entries
- `bubblePopped`: Handled by the Friends Choice Dialog
- `inactiveNoBubble12Hours`: Reduced notification frequency (still in schema for backend compatibility)
- `mentions`: Handled differently in the app

This creates a clean separation between immediate actions (dialogs) and informational updates (notifications).

## Notification Payload Structure

For the notification icon system to work correctly, each notification category requires specific payload fields:

### User-Related Notifications (Notification List Only)

- **contactRequestDeclined**:
  ```json
  {
    "userId": "user_id_of_decliner",
    "userName": "Name of user who declined"
  }
  ```

### ❌ **Dialog-Based Payload Structures (No Longer in Notification List)**

The following payload structures are now used for **dialog display** instead of notification list:

- **contactRequestReceived** → `ContactRequestDialog`:
  ```json
  {
    "senderId": "user_id_of_sender",
    "senderName": "Name of sender"
  }
  ```

- **contactRequestAccepted** → `ContactshipEstablishedDialog`:
  ```json
  {
    "userId": "user_id_of_accepter",
    "userName": "Name of user who accepted"
  }
  ```

- **friendshipEstablished** → `FriendshipEstablishedDialog`:
  ```json
  {
    "friendUserId": "user_id_of_new_friend",
    "friendName": "Name of friend"
  }
  ```

- **bubbleInvitationReceived** → `BubbleInviteRequestDialog`:
  ```json
  {
    "bubbleId": "id_of_bubble",
    "bubbleName": "Name of bubble",
    "inviterId": "user_id_of_inviter",
    "inviterName": "Name of inviter"
  }
  ```

- **bubbleJoinRequestReceived** → `BubbleJoinRequestDialog`:
  ```json
  {
    "bubbleId": "id_of_bubble",
    "bubbleName": "Name of bubble",
    "requesterId": "user_id_of_requester",
    "requesterName": "Name of requester"
  }
  ```

- **bubbleMemberJoined** → `BubbleMemberJoinedDialog`:
  ```json
  {
    "bubbleId": "id_of_bubble",
    "bubbleName": "Name of bubble",
    "memberId": "user_id_of_new_member",
    "memberName": "Name of member"
  }
  ```

### Friend Communications (Notification List Only)

- **friendChatMessageReceived/friendVoiceMessageReceived/friendVideoMessageReceived**:
  ```json
  {
    "messageId": "id_of_message",
    "senderId": "user_id_of_sender",
    "senderName": "Name of sender"
  }
  ```

- **friendAudioCallIncoming/friendVideoCallIncoming/friendScreenShareIncoming/friendMissedCall/friendCallInProgress**:
  ```json
  {
    "callId": "id_of_call",
    "callerId": "user_id_of_caller",
    "callerName": "Name of caller"
  }
  ```

### Other Notification Types

Other notification types don't require specific payload fields for icon display, but may include relevant data for handling tap actions or displaying additional information.

### Implementation Details

The notification icon system is implemented in `notifications_dialog.dart` with three key methods:

1. **`_buildNotificationIcon`**: Main method that determines which icon to display
2. **`_shouldShowUserProfilePicture`**: Determines if a notification category should display a user profile picture
3. **`_getUserIdFromPayload`**: Extracts the relevant user ID from a notification payload
4. **`_getCustomImagePath`**: Returns the custom image path for a notification category
5. **`_isRemovedNotificationType`**: Checks if a notification type is one of the removed types

### `