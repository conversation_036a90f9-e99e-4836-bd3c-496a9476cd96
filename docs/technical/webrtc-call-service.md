# WebRTC Call Service Documentation

## 📋 Overview

The WebRTC Call Service provides comprehensive video, audio, and screen sharing capabilities for the Hopen backend. It handles call signaling, session management, and real-time communication between users.

## 🏗️ Architecture

### Components

1. **Call Service** (`/microservices/call/`)
   - Main service handling call management
   - REST API endpoints for call operations
   - Database models for call persistence

2. **Signaling Hub** (`signaling.go`)
   - WebSocket-based signaling server
   - Real-time message routing between participants
   - Connection management and cleanup

3. **PubSub Integration** (`subscribers.go`)
   - Event-driven architecture for call events
   - Integration with notification service
   - Asynchronous message processing

4. **Database Schema**
   - `calls` table: Call session information
   - `call_participants` table: Participant management
   - `webrtc_sessions` table: WebRTC connection state

## 🗄️ Database Schema

### Calls Table
```sql
CREATE TABLE calls (
    id VARCHAR(255) PRIMARY KEY,
    caller_id VARCHAR(255) NOT NULL,
    callee_id VARCHAR(255),           -- For direct calls
    bubble_id VARCHAR(255),           -- For group calls
    type VARCHAR(50) NOT NULL,        -- 'direct' or 'group'
    status VARCHAR(50) NOT NULL,      -- 'initiating', 'ringing', 'active', 'ended', etc.
    with_video BOOLEAN DEFAULT false,
    with_audio BOOLEAN DEFAULT true,
    with_screen BOOLEAN DEFAULT false,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ended_at TIMESTAMP WITH TIME ZONE,
    duration INTEGER,                 -- in seconds
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Call Participants Table
```sql
CREATE TABLE call_participants (
    id VARCHAR(255) PRIMARY KEY,
    call_id VARCHAR(255) NOT NULL REFERENCES calls(id),
    user_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,      -- 'invited', 'joined', 'left', 'declined'
    with_video BOOLEAN DEFAULT false,
    with_audio BOOLEAN DEFAULT true,
    with_screen BOOLEAN DEFAULT false,
    joined_at TIMESTAMP WITH TIME ZONE,
    left_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### WebRTC Sessions Table
```sql
CREATE TABLE webrtc_sessions (
    id VARCHAR(255) PRIMARY KEY,
    call_id VARCHAR(255) NOT NULL REFERENCES calls(id),
    user_id VARCHAR(255) NOT NULL,
    peer_id VARCHAR(255) NOT NULL,
    local_sdp TEXT,
    remote_sdp TEXT,
    ice_candidates JSONB DEFAULT '[]'::jsonb,
    connection_state VARCHAR(50) DEFAULT 'new',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔌 API Endpoints

### REST API

#### 1. Get Call Service Info
```http
GET /api/call/
```
**Response:**
```json
{
  "message": "Call service ready",
  "data": {
    "description": "Video call functionality",
    "endpoints": [
      "POST /api/call/initiate",
      "POST /api/call/answer",
      "POST /api/call/end"
    ]
  }
}
```

#### 2. Initiate Call
```http
POST /api/call/initiate
Authorization: Bearer <jwt_token>
Content-Type: application/json
```
**Request Body:**
```json
{
  "type": "direct",           // "direct" or "group"
  "callee_id": "user123",     // For direct calls
  "bubble_id": "bubble456",   // For group calls
  "with_video": true,
  "with_audio": true,
  "with_screen": false
}
```
**Response:**
```json
{
  "call_id": "call_789",
  "status": "initiating",
  "participants": [
    {
      "user_id": "caller123",
      "status": "joined"
    },
    {
      "user_id": "callee456",
      "status": "invited"
    }
  ]
}
```

#### 3. Answer Call
```http
POST /api/call/answer
Authorization: Bearer <jwt_token>
Content-Type: application/json
```
**Request Body:**
```json
{
  "call_id": "call_789",
  "accept": true,
  "with_video": true,
  "with_audio": true
}
```

#### 4. End Call
```http
POST /api/call/end
Authorization: Bearer <jwt_token>
Content-Type: application/json
```
**Request Body:**
```json
{
  "call_id": "call_789"
}
```

#### 5. Update Media State
```http
POST /api/call/update-media
Authorization: Bearer <jwt_token>
Content-Type: application/json
```
**Request Body:**
```json
{
  "call_id": "call_789",
  "with_video": false,
  "with_audio": true,
  "with_screen": true
}
```

### WebSocket Signaling

#### Connection
```
GET /call/{callID}/signaling
Authorization: Bearer <jwt_token>
Upgrade: websocket
```

#### Signaling Messages

**Offer/Answer (SDP Exchange):**
```json
{
  "type": "offer",
  "call_id": "call_789",
  "from_user": "user123",
  "to_user": "user456",
  "data": {
    "sdp": "v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\n...",
    "type": "offer"
  }
}
```

**ICE Candidates:**
```json
{
  "type": "ice-candidate",
  "call_id": "call_789",
  "from_user": "user123",
  "to_user": "user456",
  "data": {
    "candidate": "candidate:1 1 UDP 2130706431 ************* 54400 typ host",
    "sdpMid": "0",
    "sdpMLineIndex": 0
  }
}
```

**User Events:**
```json
{
  "type": "user-joined",
  "call_id": "call_789",
  "from_user": "user456",
  "data": {
    "with_video": true,
    "with_audio": true
  }
}
```

**State Changes:**
```json
{
  "type": "state-changed",
  "call_id": "call_789",
  "from_user": "user123",
  "data": {
    "with_video": false,
    "with_audio": true,
    "with_screen": true
  }
}
```

## 🔄 Call Flow

### Direct Call Flow

1. **Initiation:**
   - Caller sends `POST /api/call/initiate`
   - Call record created in database
   - Participants added to call
   - Push notification sent to callee
   - Call event published to PubSub

2. **Signaling Setup:**
   - Both users connect to WebSocket endpoint
   - Signaling hub manages connections
   - Users exchange SDP offers/answers
   - ICE candidates exchanged

3. **Media Connection:**
   - WebRTC peer connection established
   - Media streams (audio/video) start flowing
   - Connection state tracked in database

4. **Call Management:**
   - Media state changes (mute/unmute, video on/off)
   - Screen sharing toggle
   - Participant management

5. **Call Termination:**
   - Either user sends `POST /api/call/end`
   - All participants notified
   - WebRTC sessions cleaned up
   - Call duration calculated and stored

### Group Call Flow

Similar to direct calls but with multiple participants:
- Multiple WebRTC peer connections (mesh topology)
- Broadcast signaling to all participants
- Dynamic participant management (join/leave)

## 🔧 Integration Points

### Flutter App Integration

The Flutter app should:

1. **Connect to WebSocket:**
```dart
final websocket = WebSocketChannel.connect(
  Uri.parse('ws://*********:4000/call/$callId/signaling'),
  headers: {'Authorization': 'Bearer $jwtToken'}
);
```

2. **Handle Signaling Messages:**
```dart
websocket.stream.listen((message) {
  final data = jsonDecode(message);
  switch (data['type']) {
    case 'offer':
      handleOffer(data);
      break;
    case 'answer':
      handleAnswer(data);
      break;
    case 'ice-candidate':
      handleIceCandidate(data);
      break;
  }
});
```

3. **Send Signaling Messages:**
```dart
void sendOffer(RTCSessionDescription offer) {
  websocket.sink.add(jsonEncode({
    'type': 'offer',
    'data': {
      'sdp': offer.sdp,
      'type': offer.type,
    },
    'to_user': targetUserId,
  }));
}
```

### Flutter WebRTCService (Client-Side)

The mobile application leverages a dedicated `WebRTCService` located at
`lib/provider/services/webrtc/webrtc_service.dart`. Key capabilities:

* **Unified Start Call API** – `startVoiceCall` and `startVideoCall` funnel to a
  private `_startCall` that encapsulates media-stream acquisition, optional
  screen-share capture, peer-connection creation, and signaling.
* **Connection Quality Telemetry** – Every 5 s a timer samples
  `RTCPeerConnection.getStats()` (stubbed for now) and emits
  `ConnectionQualityUpdate` records on `qualityStream`, containing:
  `iceState`, `connectionScore`, RTT, jitter, packet counters, and bitrate
  estimates.
* **Reactive Streams** –
  - `callStateStream` publishes `CallModel` updates for UI binding.
  - `callEndedStream` signals ended calls so pages can dispose resources.
* **Multi-Device Safety** – Each call is keyed by a UUID allowing concurrent
  mesh connections in group calls.
* **Automatic Monitoring & Recovery** – ICE / PeerConnection state callbacks
  transition the call's `CallStatus` and trigger teardown when failures occur.
* **Data-Channel Hook** – A control channel (`quality_metrics`) is opened for
  out-of-band quality messages between peers.

Developers should subscribe to `qualityStream` inside the call UI to render
real-time network diagnostics (e.g., connection bars, bitrate charts).

### Notification Service Integration

The call service integrates with the notification service for:
- Incoming call notifications
- Call state change notifications
- Push notifications for missed calls

### MQTT Integration

Real-time call events are also published to MQTT topics:
- `calls/{user_id}/incoming` - Incoming call notifications
- `calls/{call_id}/events` - Call state changes
- `calls/{user_id}/missed` - Missed call notifications

## 🔒 Security Features

1. **Authentication:**
   - JWT token required for all endpoints
   - WebSocket connections authenticated
   - User authorization for call participation

2. **Authorization:**
   - Users can only join calls they're invited to
   - Bubble members can join group calls
   - Call initiator has admin privileges

3. **Data Protection:**
   - SDP and ICE candidates stored securely
   - Connection state tracking
   - Automatic cleanup of ended calls

## 📊 Monitoring & Analytics

### Metrics Tracked

1. **Call Metrics:**
   - Call duration
   - Connection success rate
   - Media quality indicators
   - Participant count

2. **Performance Metrics:**
   - WebSocket connection count
   - Signaling message latency
   - Database query performance

3. **Error Tracking:**
   - Failed call attempts
   - Connection failures
   - Signaling errors

### Health Checks

The call service provides health status in the main health endpoint:
```json
{
  "status": "ok",
  "services": {
    "call": "ready"
  }
}
```

## 🚀 Deployment Considerations

### Docker Configuration

The call service is included in the main backend container and requires:
- PostgreSQL database for call persistence
- WebSocket support for signaling
- NATS for PubSub messaging

### Scaling Considerations

For production scaling:
1. **Horizontal Scaling:** Multiple backend instances with load balancer
2. **WebSocket Sticky Sessions:** Ensure users connect to same instance
3. **Database Optimization:** Proper indexing for call queries
4. **Media Server:** Consider dedicated media servers (Kurento, Janus) for large-scale deployments

### Production Optimizations

1. **Connection Limits:** Configure WebSocket connection limits
2. **Message Rate Limiting:** Prevent signaling message spam
3. **Cleanup Jobs:** Regular cleanup of old call records
4. **Monitoring:** Comprehensive logging and metrics collection

## 🔧 Configuration

### Environment Variables

```bash
# WebRTC Configuration
WEBRTC_STUN_SERVERS=stun:stun.l.google.com:19302
WEBRTC_TURN_SERVERS=turn:your-turn-server.com:3478
WEBRTC_TURN_USERNAME=username
WEBRTC_TURN_PASSWORD=password

# Call Service Configuration
CALL_MAX_PARTICIPANTS=10
CALL_MAX_DURATION=7200  # 2 hours in seconds
CALL_CLEANUP_INTERVAL=3600  # 1 hour in seconds
```

### Feature Flags

```bash
# Feature toggles
ENABLE_SCREEN_SHARING=true
ENABLE_GROUP_CALLS=true
ENABLE_CALL_RECORDING=false
MAX_GROUP_CALL_PARTICIPANTS=10
```

## 🧪 Testing

### Unit Tests
- Call service logic
- Signaling message handling
- Database operations

### Integration Tests
- WebSocket connections
- PubSub message flow
- End-to-end call scenarios

### Load Tests
- Multiple concurrent calls
- WebSocket connection limits
- Database performance under load

## 📚 Additional Resources

- [WebRTC Specification](https://webrtc.org/)
- [Flutter WebRTC Plugin](https://pub.dev/packages/flutter_webrtc)
- [Gorilla WebSocket Documentation](https://github.com/gorilla/websocket)
- [PostgreSQL JSONB Documentation](https://www.postgresql.org/docs/current/datatype-json.html) 