# Enhanced Error System Documentation

## Overview

Our enhanced error system builds upon the existing `Result<T>` foundation with powerful functional programming patterns for better composition, error handling, and code clarity.

## Key Components

### 1. Result<T> - Core Type
The foundation remains the same with enhanced methods:

```dart
// Basic usage
final result = Result.success("Hello");
final error = Result.failure(NetworkError(message: "Failed"));

// Enhanced chaining
final enhanced = result
  .map((data) => data.toUpperCase())
  .filter((data) => data.isNotEmpty, () => ValidationError(message: "Empty"))
  .tap((data) => print("Success: $data"))
  .recover((error) => "Default value");
```

### 2. TaskResult<T> - Async Operations
Type alias for `Future<Result<T>>` with enhanced async operations:

```dart
TaskResult<String> fetchUserName(String userId) async {
  return safeApiCall(() => api.getUserName(userId));
}

// Chain async operations
final result = fetchUserName("123")
  .flatMapAsync((name) => fetchUserProfile(name))
  .mapAsync((profile) => profile.displayName)
  .timeout(Duration(seconds: 5));
```

### 3. Do Notation - Clean Composition
Eliminates nested flatMap calls:

```dart
// Without Do notation (nested and hard to read)
final result = getUserById(userId)
  .flatMap((user) => getBubbleById(bubbleId)
    .flatMap((bubble) => checkPermissions(user.id, bubble.id)
      .map((permissions) => BubbleWithPermissions(bubble, permissions))));

// With Do notation (clean and linear)
final result = Result.Do(($) {
  final user = $(getUserById(userId));
  final bubble = $(getBubbleById(bubbleId));
  final permissions = $(checkPermissions(user.id, bubble.id));
  
  return BubbleWithPermissions(bubble, permissions);
});
```

### 4. Option<T> - Nullable Values
Functional approach to handling nullable values:

```dart
// Instead of null checks
String? findUserName(String id) => users[id]?.name;
final name = findUserName("123") ?? "Unknown";

// Use Option for better composition
Option<String> findUserNameOption(String id) => 
  Option.fromNullable(users[id]?.name);

final result = findUserNameOption("123")
  .filter((name) => name.isNotEmpty)
  .map((name) => name.toUpperCase())
  .getOrElse("Unknown");
```

## Practical Examples

### Repository Implementation

```dart
class BubbleRepositoryImpl extends BaseRepository {
  @override
  Future<Result<BubbleEntity>> getBubble(BubbleId bubbleId) async {
    return safeApiCallWithErrorMapping(
      () => _remoteDataSource.get('/bubbles/${bubbleId.value}'),
      (error) => _mapToBubbleError(error),
    ).flatMapAsync((response) => 
      BubbleEntity.fromJson(response).toFuture()
    );
  }

  Future<Result<BubbleEntity>> getBubbleWithValidation(BubbleId bubbleId) async {
    return TaskResult.Do(($) async {
      final bubble = await $(getBubble(bubbleId));
      final validation = $(validateBubbleAccess(bubble));
      final enriched = await $(enrichBubbleData(bubble));
      
      return enriched;
    });
  }
}
```

### Use Case Implementation

```dart
class LoadBubbleUseCase extends EnhancedUseCase<BubbleEntity, LoadBubbleParams> {
  @override
  Future<BubbleEntity> executeImpl(LoadBubbleParams params) async {
    return TaskResult.Do(($) async {
      final user = await $(getCurrentUser());
      final bubble = await $(getBubble(params.bubbleId));
      final permissions = await $(checkPermissions(user.id, bubble.id));
      
      return bubble.copyWith(userPermissions: permissions);
    });
  }

  @override
  Result<LoadBubbleParams> validateParams(LoadBubbleParams params) {
    return Validate.notEmpty(params.bubbleId, 
      () => ValidationError(message: "Bubble ID is required"));
  }

  @override
  Future<void> onSuccess(BubbleEntity result) async {
    // Log successful bubble load
    analytics.track('bubble_loaded', {'bubbleId': result.id.value});
  }
}
```

### Bloc Integration

```dart
class BubbleBloc extends Bloc<BubbleEvent, BubbleState> {
  Future<void> _onLoadBubble(LoadBubble event, Emitter<BubbleState> emit) async {
    emit(const BubbleLoading());

    final result = await _loadBubbleUseCase.executeWithValidation(
      LoadBubbleParams(bubbleId: event.bubbleId)
    );

    result.fold(
      onSuccess: (bubble) => emit(BubbleLoaded(bubble)),
      onFailure: (error) => emit(BubbleError(error)),
    );
  }
}
```

## Advanced Patterns

### Parallel Operations
```dart
Future<Result<BubbleWithDetails>> loadBubbleWithDetails(String bubbleId) async {
  final tasks = [
    getBubble(bubbleId),
    getBubbleMembers(bubbleId),
    getBubbleMessages(bubbleId),
  ];
  
  return ResultUtils.sequenceAsync(tasks)
    .mapAsync((results) => BubbleWithDetails(
      bubble: results[0],
      members: results[1],
      messages: results[2],
    ));
}
```

### Error Recovery
```dart
Future<Result<UserProfile>> getUserProfileWithFallback(String userId) async {
  return getUserProfile(userId)
    .recoverWithAsync((error) => getCachedUserProfile(userId))
    .recoverWithAsync((error) => getDefaultUserProfile())
    .tapErrorAsync((error) => logError('Failed to get user profile', error));
}
```

### Conditional Operations
```dart
Future<Result<BubbleEntity>> joinBubbleIfAllowed(String bubbleId, String userId) async {
  return TaskResult.Do(($) async {
    final bubble = await $(getBubble(bubbleId));
    final user = await $(getUser(userId));
    
    final canJoin = $(Validate.that(
      bubble,
      (b) => b.memberCount < b.maxMembers && b.isActive,
      () => BubbleCapacityExceededError(
        bubbleId: bubbleId,
        currentCount: bubble.memberCount,
        maxCapacity: bubble.maxMembers,
      ),
    ).toFuture());
    
    final result = await $(joinBubble(bubbleId, userId));
    return result;
  });
}
```

## Migration Guide

### Phase 1: Start Using Enhanced Extensions
```dart
// Old way
final result = await getBubble(bubbleId);
if (result.isSuccess) {
  final bubble = result.data;
  // process bubble
} else {
  // handle error
}

// New way
await getBubble(bubbleId)
  .tapAsync((bubble) => processBubble(bubble))
  .tapErrorAsync((error) => handleError(error));
```

### Phase 2: Adopt Do Notation
```dart
// Replace nested flatMap chains with Do notation
final result = Result.Do(($) {
  final step1 = $(operation1());
  final step2 = $(operation2(step1));
  final step3 = $(operation3(step2));
  return finalResult(step1, step2, step3);
});
```

### Phase 3: Use Option for Nullable Values
```dart
// Replace null checks with Option
Option<User> findUser(String id) => Option.fromNullable(users[id]);

final userName = findUser(userId)
  .map((user) => user.name)
  .filter((name) => name.isNotEmpty)
  .getOrElse("Anonymous");
```

## Best Practices

1. **Use Do notation for complex operations** with multiple steps
2. **Prefer TaskResult over Future<Result>** for consistency
3. **Use Option for nullable values** that need composition
4. **Leverage tap methods** for side effects (logging, analytics)
5. **Use validation helpers** for parameter checking
6. **Implement error recovery** for critical operations
7. **Use parallel operations** when possible for performance

## Testing

```dart
test('should handle bubble loading with validation', () async {
  // Arrange
  final useCase = LoadBubbleUseCase(repository);
  final params = LoadBubbleParams(bubbleId: 'test-id');
  
  // Act
  final result = await useCase.executeWithValidation(params);
  
  // Assert
  expect(result.isSuccess, isTrue);
  expect(result.data.id.value, equals('test-id'));
});

test('should handle validation errors', () async {
  // Arrange
  final useCase = LoadBubbleUseCase(repository);
  final params = LoadBubbleParams(bubbleId: ''); // Invalid
  
  // Act
  final result = await useCase.executeWithValidation(params);
  
  // Assert
  expect(result.isFailure, isTrue);
  expect(result.error, isA<ValidationError>());
});
```
