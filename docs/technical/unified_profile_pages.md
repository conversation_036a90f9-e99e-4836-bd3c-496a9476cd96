# Unified Profile Pages Technical Documentation

> **Note:** A future refactoring is planned to consolidate `FriendProfilePage`, `ContactProfilePage`, and `BubblerProfilePage` into a single, configurable widget. See the [Development Roadmap](../roadmap/roadmap.md) for details.

## Overview

Hopen provides detailed profile pages for different types of connections: Friends, Contacts, and Bubble Members (Bubblers). These pages share a common design language while offering context-specific functionality appropriate to the relationship type. This document describes the implementation details, UI components, and interaction patterns across all profile page variants.

## Common Components Across Profile Pages

### Core Elements

All profile pages (`FriendProfilePage`, `ContactProfilePage`, and `BubblerProfilePage`) share these core UI elements:

1. **Gradient Background**: Uses the standard `GradientBackground` widget for the `ContactProfilePage`, and `BubblerProfilePage`. `FriendProfilePage` uses the same gradient as the `BubbleStatusCard`.
2. **App Bar**: Standard app bar with a back button and optional actions (like <PERSON><PERSON>, Block, Report).
    * Title displays `@username` if available, otherwise the user's name. Font size is responsive.
    * The `more_vert` icon opens a `PopupMenuButton` with context-specific actions (e.g., Unfriend only appears on `FriendProfilePage`).
3. **Profile Picture Area**: A large `Image.network` widget occupying the top 45% of screen height. Includes a vertical gradient fade (`ShaderMask` with `BlendMode.dstIn`) at the bottom for a smooth transition.
    * Displays a placeholder icon (`Icons.person` or `Icons.broken_image`) during loading or on error.
4. **Content Block**: Positioned starting at 40% screen height (overlapping the profile picture area slightly) via `Transform.translate`. Contains:
    * **Name Row**: Displays the user's name prominently. Font size is responsive.
        * An `OnlineStatusIndicator` appears next to the name if the user is online.
        * The `BubbleStatusBadge` widget is displayed inline after the name and online status. The badge uses standardized sizing that matches the filter chips in the Contacts page.
    * **Relationship Date**: Specific details like 'Friends since [Date]', 'Contact since [Date]', or 'Bubbler since [Date]' are shown directly below the name/status row with minimal vertical spacing. Font size is responsive and smaller than the name.
    * **Mutual Connections Area**: Displays horizontally scrolling lists of mutual friends and contacts. This section appears below the relationship date with minimal top padding.
        * Each item consists of a `CircleAvatar` (radius 14) and the connection's first name below it (small, responsive font size).
        * Each avatar+name combination is wrapped in a `SizedBox` with a fixed `width` (currently 40.0) to ensure consistent horizontal spacing regardless of name length.
        * The parent `Row` for each list uses a `spacing` property (currently 2.0) to define the gap *between* these fixed-width containers.
    * **Bubble Members Area (New)**: Displays a horizontally scrolling list of the *viewed user's* bubble members.
        * **Visibility**:
            * On `FriendProfilePage` and `ContactProfilePage`, this section only appears if the viewed user is part of *a* bubble (i.e., their `bubbleStatus` is not `BubbleStatus.noBubble`) AND the list of their bubble members is not empty.
            * On `BubblerProfilePage`, this section appears if the list of the bubbler's members is not empty.
        * **Content**: Shows the count of members in the title (e.g., "4 bubblers"). **Note:** This displays the members of the *profiled user's* bubble, not members *in common* with the viewing user.
        * **UI**: Matches the style and structure of the "Mutual Connections Area" (title, horizontal `Row` of avatars+names within fixed-width `SizedBox` containers, spacing of 2.0).
        * **Interaction**: Tapping an avatar navigates to that member's profile page (using the logic in `_buildMutualAvatar`).
        * **Data**: Currently implemented using mock data (`_bubbleMembers` list). **TODO:** Integrate with backend to fetch the actual bubble members list for the viewed user.
5. **Bottom Action Button Area**: Contains the main action button(s) relevant to the relationship.

### Navigation Flow

- Navigation to a profile page hides the bottom navigation bar.
- Navigation back from a profile page restores the bottom navigation bar.
- Mutual connection avatars provide deep-linking to other profiles.
- Each profile type has its dedicated route pattern.

### Styling Guidelines

- **Typography**:
    * Name: Responsive size (e.g., 22-30px), Bold, White (or themed color for Bubblers).
    * App Bar Title: Responsive size (e.g., 14-18px), Bold, White.
    * Section Titles ("X friends in common"): Responsive size (e.g., 12-16px), Bold, White.
    * Relationship Date Text: Responsive size (e.g., 10-14px), Regular, White70.
    * Mutual Avatar Names: Responsive size (e.g., 8-12px), Semi-bold, White.
    * Bubble Status Badge Text: Standardized size matching filter chips, Regular, White.
- **Responsiveness**: Font sizes for most text elements scale based on screen width using helper methods (e.g., `_getHeadingSize`, `_getSubheadingSize`). Avatar sizes and spacing are fixed but chosen to work well across typical mobile screen sizes.
- **Animations**:
    * `OnlineStatusIndicator` uses animated glow effect.
    * Standard platform transitions for page navigation.

## Type-Specific Components

### FriendProfilePage

- **Location**: `lib/presentation/pages/friends/friend_profile_page.dart`
- **Unique Features**:
    1. **Relationship History**:
        * Displays "Friends since [Date]" text. (Note: "Last bubble" text has been removed).
    2. **Call Action Buttons**:
        * Audio Call, Video Call, Screen Share buttons are displayed using `_buildGlassButton`. Labels below these buttons have been restored.
    3. **More Options**: Includes "Unfriend" in the `PopupMenuButton`.
    4. **Bottom Button Logic**: Complex seven-state logic based on both users' bubble statuses.
    5. **Bubble Status Badge**: Uses the shared `BubbleStatusBadge` widget with standardized sizing.

### ContactProfilePage

- **Location**: `lib/presentation/pages/contacts/contact_profile_page.dart`
- **Unique Features**:
    1. **No Call Buttons**: Contact profiles do not offer direct calling options.
    2. **More Options**: Excludes "Unfriend".
    3. **Bottom Button Logic**: Highly contextual logic determines the text, state (enabled/disabled/pending), and action (navigation, request, info dialog) of the bottom button. It depends on:
        * Whether the viewed user **is a contact** of the viewing user (passed via `isContact` parameter).
        * The **bubble status** of the viewed contact (passed via `bubbleStatus` parameter, e.g., `noBubble`, `notFullBubble`, `fullBubble`).
        * The **bubble status** of the *viewing user* (obtained from state management, e.g., BLoC/Provider).
        * Whether the viewing user and contact **are in the same bubble** (obtained from state management).
        * Whether a bubble-related **request is currently pending** between the two users (local state, ideally driven by BLoC).

        **Key Scenarios Implemented:**
        * **Not a Contact**: Button shows "Send contact request". Tapping initiates the request, shows a confirmation dialog, and temporarily updates the button state to "Contact request sent".
        * **Already in Same Bubble**: Impossible, because it is not a contact then, but a bubbler. Bubblers do not appear on the `contactpage`, but on the `bubblepage`.
        * **Contact (Not Full Bubble) & User (No Bubble)**: Button shows "Ask to join bubble". Tapping initiates the request, shows a confirmation dialog, and updates button state to "Bubble request sent".
        * **Contact (No Bubble) & User (No Bubble)**: Button shows "Start a bubble together". Tapping initiates the request, shows a confirmation dialog, and updates button state to "Bubble creation request sent".
        * **Contact (Bubble Full) & User (No Bubble)**: Button shows "Contact" (disabled style). Tapping shows an info dialog explaining the contact's bubble is full.
        * **Contact (Not Full Bubble) & User (Not Full Bubble, Different Bubble)**: Button shows "Contact" (disabled style). Tapping shows an info dialog explaining one cannot be in multiple bubbles.
        * **Contact (Not Full Bubble) & User (Bubble Full)**: Button shows "Contact" (disabled style). Tapping shows an info dialog explaining the user's bubble is full.
        * **Contact (Bubble Full) & User (Not Full Bubble)**: Button shows "Contact" (disabled style). Tapping shows an info dialog explaining bubbles are full and/or one cannot be in multiple bubbles.
        * **Contact (Bubble Full) & User (Bubble Full)**: Button shows "Contact" (disabled style). Tapping shows an info dialog explaining both bubbles are full.
        * **Contact (No Bubble) & User (Not Full Bubble)**: Button shows "Ask to join your bubble". Tapping initiates the request, shows confirmation, and updates button to "Bubble invite sent".
        * **Contact (No Bubble) & User (Bubble Full)**: Button shows "Contact" (disabled style). Tapping shows an info dialog explaining the user's bubble is full.
        * **Pending Request**: If a relevant request is pending, the button shows the appropriate text (e.g., "Bubble request sent") and is disabled.

        **(Implementation Note:** The code uses `widget.bubbleStatus` for the contact's status and has TODOs indicating where to fetch the viewing user's status and `areInSameBubble` flag from the state management system.)
    4. **Bubble Status Badge**: Uses the shared `BubbleStatusBadge` widget with standardized sizing.
    5. **Relationship History**: Displays "Contact since [Date]" text if available.

### BubblerProfilePage

- **Location**: `lib/presentation/pages/bubble/bubbler_profile_page.dart`
- **Unique Features**:
    1. **Colored Name**: Name displays using the color assigned to this bubbler in the bubble.
    2. **Simplified Bottom Button**: Always shows a disabled "Bubbler" button.
    3. **More Options**: Excludes "Unfriend".
    4. **Bubble Status Badge**: Displays the `BubbleStatusBadge` widget.
    5. **Relationship History**: Displays "Bubbler since [Date]" text if available.

## Interactive Features

### Mutual Connection Interaction

Both "friends in common" and "contacts in common" sections display interactive avatars. When a user taps on any mutual connection:

1. The application determines the appropriate profile type (friend or contact).
2. Navigates to the corresponding profile page using `MaterialPageRoute`.
3. Passes mock data for development (in production, would fetch actual user data).
4. Maintains navigation stack for proper back-button functionality.

Implementation details:
```dart
// Inside the .map() function for the avatar Row in each profile page
// Wrap avatar in SizedBox for consistent spacing
return SizedBox(
  width: 40.0, // Fixed width
  child: _buildMutualAvatar(
    connection.imageUrl,
    connection.name,
  ),
);

// Inside _buildMutualAvatar method
GestureDetector(
  onTap: () {
    // Determine if friend or contact
    final bool isMutualFriend = name.startsWith('Friend'); // Example logic

    if (isMutualFriend) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => FriendProfilePage(
            // ... pass required data ...
          ),
        ),
      );
    } else {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ContactProfilePage(
            // ... pass required data ...
          ),
        ),
      );
    }
  },
  child: Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      CircleAvatar(radius: 14, ...), // Small radius
      const SizedBox(height: 3),
      Flexible(
        child: Text(
          name.split(' ')[0], // First name only
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: _getBodyTextSize(context) - 4, // Small, responsive
            // ... other style props ...
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    ],
  ),
)


## Navigation Patterns

### Path-Based Navigation
- `FriendProfilePage`: Accessed via `/friends/:friendId`
- `ContactProfilePage`: Accessed via `/contact/:contactId`
- `BubblerProfilePage`: Accessed via `/bubble/member/:memberId`

### Data Passing
Profile pages receive data through:
1. **Path Parameters**: IDs extracted from route paths.
2. **Extra Parameters**: Additional data passed through router's `extra` parameter (e.g., `memberColor` for `BubblerProfilePage`, `UserContact` object for `ContactProfilePage`).

### Navigation Bar Management
All profile pages use `NavBarVisibilityNotifier` to control the visibility of the bottom navigation bar:
```dart
final navNotifier = Provider.of<NavBarVisibilityNotifier>(context, listen: false);
navNotifier.hideNavBar();
// Navigation code...
if (context.mounted) {
  navNotifier.showNavBar();
}


## Implementation Considerations

### State Management
Profile pages currently use local state with mock data. Future implementation will:
- Fetch real user data from API endpoints.
- Implement proper state management (BLoC or Provider) to handle loading/error states.
- Cache profile data to reduce network requests.

### User Stories
For a complete list of user stories related to profiles and user interactions, see the [User Stories](../technical/user_stories.md) documentation, particularly the "Profile Viewing" section.

### Performance Optimization
- Lazy loading of mutual connection avatars (currently handled by `Image.network`).
- Image caching for profile photos (`Image.network` provides basic caching).
- Optimized rebuilds using `StatefulWidget` and appropriate `setState` calls. Consider `const` widgets where possible.

### Accessibility
- Ensure proper contrast for all text elements.
- Maintain adequate touch target sizes (avatar `GestureDetector` covers the `SizedBox`).
- Consider semantics labels for icons and potentially for mutual connection items. Note that avatar names are now quite small, potentially impacting readability for some users.

## Testing Strategy

### Unit Tests
- Verify profile data retrieval and parsing logic.
- Test navigation logic and data passing.
- Test responsive text size helper methods.

### Widget Tests
- Validate rendering of all UI components, including the translated content block.
- Test interaction with mutual connection avatars and navigation.
- Verify correct display of online status indicator and bubble status badge (including gradient for "in a bubble").
- Verify call button label visibility/absence.

### Integration Tests
- Test the entire profile viewing flow, including navigation from other pages (e.g., Friends, Contacts, Bubble).
- Validate navigation between different profile types via mutual connections.
- Test the back navigation behavior and nav bar visibility restoration.
