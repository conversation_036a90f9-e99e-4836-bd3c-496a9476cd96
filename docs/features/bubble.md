A bubble is a group of 2 to 5 people.

It is created when :
- Someone ask one of his contacts to start a bubble together, and his contact accept the request.

Someone can join a bubble when :
- He is not in a bubble
- The bubble is not full (5 people)
- He is not banned from the bubble
- He is not already a member of the bubble
- He is not already invited to the bubble
- He is not already requested to join the bubble
- He is not already proposed to join the bubble
- The bubble is not expired
- The bubble is not dissolved
- The bubble is not archived
- The bubble is not deleted
- The bubble is not private

The different way someone can join a bubble are :
- He is invited by a member of the bubble
- He request to join the bubble
- He is proposed to join the bubble by a member of the bubble

When a bubble began, it has a countdown of 90 days.
When someone joins a bubble, the countdown is augmented by 30 days.
A bubble's countdown can't go beyond 90 days. For example, if a bubble has a countdown of 80 days, and someone joins it, the new countdown will be 90 days.
When someone leave a bubble, or is kicked out of a bubble, the countdown is not modified.

When a bubble's countdown reach 0, the bubble is expired.
When a bubble has less than 2 active members, the bubble is dissolved.
When a bubble is expired or dissolved, it is archived.
When a bubble is archived, it can't be joined anymore.
When a bubble is archived, it can't be invited to anymore.
When a bubble is archived, it can't be proposed to anymore.
When a bubble is archived, it can't be requested to join anymore.
When a bubble is archived, it can't be kicked out of anymore.
When a bubble is archived, it can't be left anymore.
When a bubble is archived, it can't be dissolved anymore.
When a bubble is archived, it can't be expired anymore.

When a bubble is created, it is in the "creating" state.
When a bubble is joined by its second member, it is in the "active" state.
When a bubble is expired, it is in the "expired" state.
When a bubble is dissolved, it is in the "dissolved" state.
When a bubble is archived, it is in the "archived" state.

When a bubble is in the "creating" state, it can't be joined.
When a bubble is in the "creating" state, it can't be invited to.
When a bubble is in the "creating" state, it can't be proposed to.
When a bubble is in the "creating" state, it can't be requested to join.
When a bubble is in the "creating" state, it can't be kicked out of.

When a bubble is in the "active" state, it can be joined.
When a bubble is in the "active" state, it can be invited to.
When a bubble is in the "active" state, it can be proposed to.
When a bubble is in the "active" state, it can be requested to join.
When a bubble is in the "active" state, it can be kicked out of.

When a bubble is in the "expired" state, it can't be joined.
When a bubble is in the "expired" state, it can't be invited to.
When a bubble is in the "expired" state, it can't be proposed to.
When a bubble is in the "expired" state, it can't be requested to join.
When a bubble is in the "expired" state, it can't be kicked out of.

When a bubble is in the "dissolved" state, it can't be joined.
When a bubble is in the "dissolved" state, it can't be invited to.
When a bubble is in the "dissolved" state, it can't be proposed to.
When a bubble is in the "dissolved" state, it can't be requested to join.

Bubble have no description.

When a bubble expires ( countdown reach 0), a friend request is proposed to all members of the bubble.
The friend request is accepted if and only if both users accept it.

All of the ui of the request (join, start, propose, invite, kickout, friend request) are in @hopen/lib/presentation/widgets/requests

The bubble members and the infos of the bubble the viewing user is in are in @hopen/lib/presentation/pages/bubble


