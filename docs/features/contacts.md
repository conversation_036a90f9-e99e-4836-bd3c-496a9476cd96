# Contacts Feature Technical Documentation

## Overview

The Contacts page serves as the primary interface for managing connections outside of the user's immediate bubble members and friends. It allows users to discover others, initiate contact requests, and manage bubble invitations based on a set of defined rules. This page is powered by the `ContactsBloc` for state management and data fetching.

## Data Models & Enums

### `UserContact`

* **Location**: `lib/presentation/pages/contacts/contacts_page.dart` (Defines the `UserContact` class structure used by the UI and BLoC states).
* **Key Properties**:
    * `id`: Unique user identifier.
    * `name`: User's display name.
    * `username`: Optional username (`String?`).
    * `imageUrl`: Path/URL to the user's profile image.
    * `bubbleStatus`: The user's current bubble status (`BubbleStatus` enum).
    * `relationshipType`: The relationship between the current user and this user (`RelationshipType` enum).
    * `isOnline`: Boolean indicating online status.
    * `contactSince`: Optional `DateTime` indicating when the contact relationship was established.

### `BubbleMembershipStatus` Enum

* **Location**: `lib/presentation/models/bubble_membership_status.dart`
* **Values**:
    * `noBubble`: User is not currently part of any bubble.
    * `inABubble`: User is part of a different bubble.
        * **Display**: In `BubbleStatusBadge`, this status now uses a **gradient background** (`#00C4FF` to `#00A3E0`) with white text.
    * `bubbleFull`: User is part of a different bubble that has reached its maximum capacity (5 members).

### `RelationshipType` Enum

* **Location**: `lib/presentation/models/relationship_type.dart`
* **Values**:
    * `none`: No established connection.
    * `contact`: Mutually agreed contacts. Prerequisite for most bubble interactions.
    * `friend`: A closer relationship.
    * `bubbler`: Member of the current user's bubble.
    * `pending`: A contact or bubble request is pending.
    * `blocked`: User is blocked.

## State Management: `ContactsBloc`

The Contacts feature utilizes the BLoC pattern for robust state management and separation of concerns.

* **Location**: `lib/statefulbusinesslogic/bloc/contacts/contacts_bloc.dart`
* **Dependencies**:
    * `ContactsRepository`: Injected to handle data operations (fetching contacts, sending requests).

### Events (`lib/statefulbusinesslogic/bloc/contacts/contacts_event.dart`)

* **`LoadContacts`**: Dispatched to fetch the initial list of all potential contacts and users.
* **`SearchContacts(String searchTerm)`**: Dispatched when the user types in the search bar to filter the current list of contacts.

### States (`lib/statefulbusinesslogic/bloc/contacts/contacts_state.dart`)

* **`ContactsInitial`**: The initial state before any data loading has begun.
* **`ContactsLoading`**: Indicates that contact data is currently being fetched (e.g., from the repository). The UI typically shows a loading indicator.
* **`ContactsLoaded(List<UserContact> contacts)`**: Represents a successful data fetch. It holds the list of `UserContact` objects.
    * The `contacts_page.dart` UI consumes this state, applies local UI filters (search query, filter chips), and then displays the results.
* **`ContactsError(String message)`**: Indicates an error occurred during data fetching or processing. The UI displays the error message.

### Data Flow

1. **Initialization**: When `ContactsPage` is initialized, it dispatches `LoadContacts` to the `ContactsBloc`.
2. **Data Fetching**: `ContactsBloc`, upon receiving `LoadContacts`, calls the `getContacts()` method of the injected `ContactsRepository`.
3. **Mock Data**: Currently, `ContactsRepositoryImpl` provides a mock list of 30 `UserContact` objects (15 `contact`, 15 `none`), simulating a backend response. This includes diverse names, usernames, profile photos, online statuses, and bubble statuses.
4. **State Emission**: `ContactsBloc` emits `ContactsLoading` while fetching, then `ContactsLoaded` with the retrieved contacts, or `ContactsError` if an issue occurs.
5. **UI Update**: `ContactsPage` uses a `BlocBuilder<ContactsBloc, ContactsState>` to listen to state changes.
    * When `ContactsLoaded` is received, the page takes `state.contacts`, applies its local UI filters (`_searchQuery`, `_selectedBubbleStatus`, `_selectedRelationshipFilter` via `_filterContacts` method), and updates the `ListView.builder`.
6. **Search**: Typing in the search bar updates `_searchQuery` in `_ContactsPageState`, which re-triggers the `_filterContacts` method on the currently loaded `state.contacts` from the BLoC. The `SearchContacts` BLoC event is also available for server-side search if implemented later.

## Repository Layer

### `ContactsRepository` (Interface)

* **Location**: `lib/repositories/contacts_repository.dart`
* **Defines the contract for contact-related data operations**:
    * `Future<List<UserContact>> getContacts()`
    * `Future<void> sendContactRequest(String contactId)`
    * `Future<void> sendBubbleRequest({required String contactId, required String requestType})`

### `ContactsRepositoryImpl` (Implementation)

* **Location**: `lib/repositories/contacts_repository_impl.dart`
* **Implements `ContactsRepository`**.
* Currently provides mock implementations for all methods, including returning the list of 30 mock `UserContact` objects for `getContacts()`. This is where future API integrations for contacts will reside.

## Dependency Injection

* `ContactsRepositoryImpl` is registered as a lazy singleton for the `ContactsRepository` interface in `lib/repositories/di/injection_container.dart`.
* `ContactsBloc` is registered as a factory, with `ContactsRepository` injected, in `injection_container.dart`.
* `ContactsBloc` is provided to the widget tree via `MultiProvider` in `lib/main.dart`.

## UI Components

### `ContactsPage`

* **Location**: `lib/presentation/pages/contacts/contacts_page.dart`
* **State Management**: Now primarily driven by `ContactsBloc`. Local `_ContactsPageState` still manages UI-specific state like search query and filter chip selections.
* **Data Source**: Retrieves the master list of contacts from `ContactsBloc`'s `ContactsLoaded` state.
* **Filtering**:
    * The `_filterContacts(List<UserContact> contacts)` method applies UI-level filtering based on the search query (`_searchQuery`) and selected filter chips (`_selectedBubbleStatus`, `_selectedRelationshipFilter`) to the list received from the BLoC.
    * Default behavior (empty search, no filters) shows only users with `RelationshipType.contact`.
    * Filters out users with `relationshipType == RelationshipType.bubbler` or `relationshipType == RelationshipType.friend`.
* **Filter Chips (`_buildFilterChips`)**:
    * Allow filtering by `BubbleStatus` (No Bubble, In Bubble, Bubble Full) and `RelationshipType` (No Relation, Contact).
    * Layout remains horizontally centered, with reduced vertical spacing.
* **List Display**: A `BlocBuilder<ContactsBloc, ContactsState>` wraps the `ListView.builder`.
    * Displays a loading indicator during `ContactsLoading`.
    * Displays an error message during `ContactsError`.
    * When `ContactsLoaded`, it passes `state.contacts` to `_filterContacts` and then renders the results using `ContactListTile` widgets.
* **Navigation**: Unchanged; uses `context.push` to `ContactProfilePage`.

### `ContactListTile`

* **Location**: `lib/presentation/pages/contacts/contacts_page.dart`
* **Purpose**: Displays an individual user entry.
* **Key Components**: Unchanged from previous description (Avatar, Name, Online Status, Bubble Status Badge, "Contact" badge).
* **Receives**: `UserContact` object and `currentUserIsInBubble` boolean (currently hardcoded in `ContactsPage`, should ideally come from a user/session BLoC).

### `ContactProfilePage`

* **Location**: `lib/presentation/pages/contacts/contact_profile_page.dart`
* **Purpose**: Displays detailed information about a specific contact.
* **Key Components Updated**:
    * **Bottom Action Button**: Logic is now driven by `ContactProfileBloc`, reflecting complex interaction rules based on both users' bubble statuses and relationship.
    * See [Profile Pages Documentation](profile_pages.md) for full details on shared components and other UI elements.

## Related Documentation

For detailed information about profile pages including `ContactProfilePage`, see [Profile Pages Documentation](profile_pages.md).

### UI Elements

1. **Search Bar**: Uses `CustomTextField` for filtering contacts.
2. **Filter Chips**: `ChoiceChip` widgets for filtering by Bubble Status and Relationship Type.
3. **Contact List**: `ListView.builder` displaying tappable contact cards.
    * Each card shows:
        * Avatar (`CircleAvatar`)
        * Name & Username
        * `OnlineStatusIndicator`
        * Bubble Status (`_buildBubbleStatusChip` - *Note: This is internal to `ContactsPage` and different from the `BubbleStatusBadge` used in profile pages*)
        * Relationship Badge (e.g., "Friend", "Contact", "Bubbler")
        * Action Button (e.g., "Add Friend", "Invite", "Requested")
4. **Add Contacts Button**: `FloatingActionButton` to initiate adding contacts (currently shows info dialog).
5. **Invite Popup**: Optional `AlertDialog` shown on initial load if `showInvitePopup` is true.

### State Management

* Uses `_ContactsPageState` (StatefulWidget) to manage search query (`_searchController`, `_searchQuery`) and selected filters (`_selectedBubbleStatus`, `_selectedRelationshipFilter`).
* Contact data (`_contacts`, `_requests`, `_pending`) is currently hardcoded sample data within the state. **TODO**: Replace with BLoC/Repository pattern.
* Filtering logic (`_filteredContacts`) is implemented within the `build` method.
