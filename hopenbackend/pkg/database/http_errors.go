package database

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ProblemDetail represents RFC 7807 Problem Details for HTTP APIs
type ProblemDetail struct {
	Type     string `json:"type"`
	Title    string `json:"title"`
	Status   int    `json:"status"`
	Detail   string `json:"detail,omitempty"`
	Instance string `json:"instance,omitempty"`
}

// Legacy error response for backward compatibility
type HTTPErrorResponse struct {
	Error   string `json:"error"`
	Code    string `json:"code,omitempty"`
	Details string `json:"details,omitempty"`
}

// HandleHTTPError converts database errors to RFC 7807 Problem Details responses
func HandleHTTPError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	// Use behavior-based error checking (Go best practice)
	switch {
	case IsNotFound(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusNotFound, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.4",
			Title:  "Not Found",
			Status: http.StatusNotFound,
			Detail: extractErrorMessage(err),
		})
	case IsDuplicate(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusConflict, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.8",
			Title:  "Conflict",
			Status: http.StatusConflict,
			Detail: extractErrorMessage(err),
		})
	case IsBubbleLimit(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusForbidden, ProblemDetail{
			Type:   "https://example.com/probs/bubble-limit-exceeded",
			Title:  "Bubble Limit Exceeded",
			Status: http.StatusForbidden,
			Detail: extractErrorMessage(err),
		})
	case IsConstraint(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusBadRequest, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.1",
			Title:  "Bad Request",
			Status: http.StatusBadRequest,
			Detail: "Invalid data provided: " + extractErrorMessage(err),
		})
	default:
		// Generic database error - don't expose internal details
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusInternalServerError, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.6.1",
			Title:  "Internal Server Error",
			Status: http.StatusInternalServerError,
			Detail: "An internal error occurred while processing your request",
		})
	}
}

// extractErrorMessage safely extracts error message from wrapped errors
func extractErrorMessage(err error) string {
	// Unwrap to get the underlying error message
	for err != nil {
		if nfErr, ok := err.(NotFoundError); ok {
			return nfErr.Error()
		}
		if dupErr, ok := err.(DuplicateError); ok {
			return dupErr.Error()
		}
		if blErr, ok := err.(BubbleLimitError); ok {
			return blErr.Error()
		}
		if cErr, ok := err.(ConstraintError); ok {
			return cErr.Error()
		}

		// Try to unwrap
		if unwrapped := errors.Unwrap(err); unwrapped != nil {
			err = unwrapped
		} else {
			break
		}
	}

	return err.Error()
}

// Specialized error handlers for different domains

// HandleUserError provides user-specific error handling with RFC 7807 compliance
func HandleUserError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	// Check for user-specific error patterns in the error message
	message := extractErrorMessage(err)

	switch {
	case IsNotFound(err) && containsUserContext(message):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusNotFound, ProblemDetail{
			Type:   "https://example.com/probs/user-not-found",
			Title:  "User Not Found",
			Status: http.StatusNotFound,
			Detail: message,
		})
	case IsDuplicate(err) && containsEmailContext(message):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusConflict, ProblemDetail{
			Type:   "https://example.com/probs/email-exists",
			Title:  "Email Already Exists",
			Status: http.StatusConflict,
			Detail: "The email address is already registered",
		})
	case IsDuplicate(err) && containsUsernameContext(message):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusConflict, ProblemDetail{
			Type:   "https://example.com/probs/username-exists",
			Title:  "Username Already Exists",
			Status: http.StatusConflict,
			Detail: "The username is already taken",
		})
	default:
		HandleHTTPError(c, err, operation)
	}
}

// HandleBubbleError provides bubble-specific error handling
func HandleBubbleError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	message := extractErrorMessage(err)

	switch {
	case IsNotFound(err) && containsBubbleContext(message):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusNotFound, ProblemDetail{
			Type:   "https://example.com/probs/bubble-not-found",
			Title:  "Bubble Not Found",
			Status: http.StatusNotFound,
			Detail: message,
		})
	case IsBubbleLimit(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusForbidden, ProblemDetail{
			Type:   "https://example.com/probs/bubble-limit-exceeded",
			Title:  "Bubble Limit Exceeded",
			Status: http.StatusForbidden,
			Detail: message,
		})
	default:
		HandleHTTPError(c, err, operation)
	}
}

// Helper functions to check error context
func containsUserContext(message string) bool {
	return message == "user not found"
}

func containsEmailContext(message string) bool {
	return message == "email already exists"
}

func containsUsernameContext(message string) bool {
	return message == "username already exists"
}

func containsBubbleContext(message string) bool {
	return message == "bubble not found"
}
