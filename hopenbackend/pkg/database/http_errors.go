package database

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
)

// HTTPErrorResponse represents a standardized HTTP error response
type HTTPErrorResponse struct {
	Error   string `json:"error"`
	Code    string `json:"code,omitempty"`
	Details string `json:"details,omitempty"`
}

// HandleHTTPError converts database errors to appropriate HTTP responses
func HandleHTTPError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	var dbErr *DatabaseError
	if errors.As(err, &dbErr) {
		handleStructuredError(c, dbErr, operation)
		return
	}

	// Handle known error types
	switch {
	case IsNotFound(err):
		c.JSO<PERSON>(http.StatusNotFound, HTTPErrorResponse{
			Error: "Resource not found",
		})
	case IsDuplicate(err):
		c.JSON(http.StatusConflict, HTTPErrorResponse{
			Error: "Resource already exists",
		})
	case IsBubbleLimit(err):
		c.<PERSON>(http.StatusForbidden, HTTPErrorResponse{
			Error: "User has reached their bubble limit",
			Code:  "BUBBLE_LIMIT_EXCEEDED",
		})
	default:
		// Generic database error
		c.JSON(http.StatusInternalServerError, HTTPErrorResponse{
			Error: "Internal server error",
		})
	}
}

// handleStructuredError handles DatabaseError with specific HTTP responses
func handleStructuredError(c *gin.Context, dbErr *DatabaseError, operation string) {
	switch dbErr.Type {
	case ErrorTypeNotFound:
		c.JSON(http.StatusNotFound, HTTPErrorResponse{
			Error: dbErr.Message,
			Code:  "NOT_FOUND",
		})

	case ErrorTypeDuplicate:
		status := http.StatusConflict
		code := "DUPLICATE_RECORD"
		
		// Provide specific error codes for common duplicates
		switch dbErr.Column {
		case "email":
			code = "EMAIL_EXISTS"
		case "username":
			code = "USERNAME_EXISTS"
		}

		c.JSON(status, HTTPErrorResponse{
			Error: dbErr.Message,
			Code:  code,
		})

	case ErrorTypeBusiness:
		switch dbErr.Code {
		case PgBubbleLimitError:
			c.JSON(http.StatusForbidden, HTTPErrorResponse{
				Error: "User has reached their bubble limit",
				Code:  "BUBBLE_LIMIT_EXCEEDED",
			})
		default:
			c.JSON(http.StatusBadRequest, HTTPErrorResponse{
				Error: dbErr.Message,
				Code:  "BUSINESS_RULE_VIOLATION",
			})
		}

	case ErrorTypeConstraint:
		c.JSON(http.StatusBadRequest, HTTPErrorResponse{
			Error: "Invalid data provided",
			Code:  "CONSTRAINT_VIOLATION",
			Details: dbErr.Message,
		})

	case ErrorTypeConnection:
		c.JSON(http.StatusServiceUnavailable, HTTPErrorResponse{
			Error: "Service temporarily unavailable",
			Code:  "DATABASE_UNAVAILABLE",
		})

	default:
		c.JSON(http.StatusInternalServerError, HTTPErrorResponse{
			Error: "Internal server error",
		})
	}
}

// Common helper functions for specific operations

// HandleUserError provides user-specific error handling
func HandleUserError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	switch {
	case errors.Is(err, ErrUserNotFound):
		c.JSON(http.StatusNotFound, HTTPErrorResponse{
			Error: "User not found",
			Code:  "USER_NOT_FOUND",
		})
	case errors.Is(err, ErrEmailExists):
		c.JSON(http.StatusConflict, HTTPErrorResponse{
			Error: "Email address is already registered",
			Code:  "EMAIL_EXISTS",
		})
	case errors.Is(err, ErrUsernameExists):
		c.JSON(http.StatusConflict, HTTPErrorResponse{
			Error: "Username is already taken",
			Code:  "USERNAME_EXISTS",
		})
	default:
		HandleHTTPError(c, err, operation)
	}
}

// HandleBubbleError provides bubble-specific error handling
func HandleBubbleError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	switch {
	case errors.Is(err, ErrBubbleNotFound):
		c.JSON(http.StatusNotFound, HTTPErrorResponse{
			Error: "Bubble not found",
			Code:  "BUBBLE_NOT_FOUND",
		})
	case errors.Is(err, ErrBubbleLimit):
		c.JSON(http.StatusForbidden, HTTPErrorResponse{
			Error: "User has reached their bubble limit",
			Code:  "BUBBLE_LIMIT_EXCEEDED",
		})
	case errors.Is(err, ErrBubbleFull):
		c.JSON(http.StatusConflict, HTTPErrorResponse{
			Error: "Bubble has reached maximum capacity",
			Code:  "BUBBLE_FULL",
		})
	default:
		HandleHTTPError(c, err, operation)
	}
}

// HandleContactError provides contact-specific error handling
func HandleContactError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	switch {
	case errors.Is(err, ErrContactNotFound):
		c.JSON(http.StatusNotFound, HTTPErrorResponse{
			Error: "Contact request not found",
			Code:  "CONTACT_NOT_FOUND",
		})
	case errors.Is(err, ErrContactExists):
		c.JSON(http.StatusConflict, HTTPErrorResponse{
			Error: "Contact request already exists",
			Code:  "CONTACT_EXISTS",
		})
	default:
		HandleHTTPError(c, err, operation)
	}
}

// HandleFriendshipError provides friendship-specific error handling
func HandleFriendshipError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	switch {
	case errors.Is(err, ErrFriendshipNotFound):
		c.JSON(http.StatusNotFound, HTTPErrorResponse{
			Error: "Friendship not found",
			Code:  "FRIENDSHIP_NOT_FOUND",
		})
	case errors.Is(err, ErrFriendshipExists):
		c.JSON(http.StatusConflict, HTTPErrorResponse{
			Error: "Friendship already exists",
			Code:  "FRIENDSHIP_EXISTS",
		})
	default:
		HandleHTTPError(c, err, operation)
	}
}
