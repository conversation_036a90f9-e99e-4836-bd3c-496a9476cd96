// ... (rest of the file remains the same)

func (c *PostgreSQLClient) InitializeSchema(ctx context.Context) error {
    // BEST PRACTICE: A single string for a single atomic transaction.
    fullSchema := `
    BEGIN;

    -- 1. EXTENSIONS AND TYPES
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    DO $$ BEGIN CREATE TYPE bubble_status AS ENUM ('active', 'expired', 'archived'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE bubble_member_status AS ENUM ('active', 'left', 'removed', 'pending', 'accepted', 'declined'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE bubble_request_type AS ENUM ('invite', 'join', 'kick', 'start'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE request_status AS ENUM ('pending', 'approved', 'rejected', 'expired'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE vote_type AS ENUM ('approve', 'reject'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE friend_request_status AS ENUM ('pending', 'accepted', 'declined', 'expired'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE notification_type AS ENUM (
        'contactRequestDeclined',
        'bubbleJoinRequestRejected',
        'bubbleInviteRequestRejected',
        'bubbleVotekickPassed',
        'bubbleChatMessageReceived',
        'bubbleVoiceMessageReceived',
        'bubbleVideoMessageReceived',
        'bubbleAudioCallIncoming',
        'bubbleVideoCallIncoming',
        'bubbleScreenShareIncoming',
        'bubbleCallInProgress',
        'bubbleCallEnded',
        'bubbleMissedCall',
        'bubblePopReminder60Days',
        'bubblePopReminder30Days',
        'bubblePopReminder20Days',
        'bubblePopReminder10Days',
        'bubblePopReminder7Days',
        'bubblePopReminder3Days',
        'bubblePopReminder24Hours',
        'bubblePopped',
        'friendChatMessageReceived',
        'friendVoiceMessageReceived',
        'friendVideoMessageReceived',
        'friendAudioCallIncoming',
        'friendVideoCallIncoming',
        'friendScreenShareIncoming',
        'friendMissedCall',
        'friendCallInProgress',
        'inactiveNoBubble12Hours',
        'inactiveNoBubble1Day',
        'inactiveNoBubble2Days',
        'inactiveNoBubble3Days',
        'inactiveNoBubble7Days',
        'statusUpdates',
        'securityAlerts',
        'appUpdates',
        'bubble_expiring_soon',
        'bubble_is_full',
        'bubble_request_rejected',
        'bubble_start_request_received',
        'bubble_call_incoming',
        'bubble_call_in_progress',
        'bubble_call_ended',
        'bubble_call_missed',
        'inactive_no_bubble_12_hours',
        'inactive_no_bubble_24_hours',
        'inactive_no_bubble_3_days',
        'inactive_no_bubble_7_days',
        'system_notification'
    ); EXCEPTION WHEN duplicate_object THEN null; END $$;

    -- 2. TABLES
    CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        username VARCHAR(40) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        display_name VARCHAR(200) NOT NULL,
        avatar_bucket_name VARCHAR(100),
        avatar_object_key VARCHAR(500),
        date_of_birth DATE NOT NULL,
        is_premium BOOLEAN DEFAULT false,
        is_active BOOLEAN DEFAULT true,
        is_private BOOLEAN DEFAULT false,
        last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        presence_status VARCHAR(20) DEFAULT 'offline',
        notification_settings JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE TABLE IF NOT EXISTS bubbles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        creator_id UUID REFERENCES users(id) ON DELETE SET NULL,
        name VARCHAR(100) NOT NULL,
        capacity INTEGER NOT NULL DEFAULT 5 CHECK (capacity >= 2 AND capacity <= 5),
        member_count INTEGER NOT NULL DEFAULT 0 CHECK (member_count >= 0),
        status bubble_status DEFAULT 'active',
        expires_at TIMESTAMP WITH TIME ZONE,
        friend_request_on_expire BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE TABLE IF NOT EXISTS bubble_members (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        bubble_id UUID NOT NULL REFERENCES bubbles(id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        status bubble_member_status DEFAULT 'active',
        joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        left_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(bubble_id, user_id, status)
    );

    CREATE TABLE IF NOT EXISTS notifications (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        type notification_type NOT NULL,
        grouping_key VARCHAR(255),
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        data JSONB DEFAULT '{}',
        is_read BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        read_at TIMESTAMP WITH TIME ZONE,
        CONSTRAINT unique_unread_grouped_notifications
        UNIQUE (user_id, grouping_key)
        WHERE is_read = false AND grouping_key IS NOT NULL
    );

    CREATE TABLE IF NOT EXISTS media_files (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        filename VARCHAR(255) NOT NULL,
        original_filename VARCHAR(255) NOT NULL,
        content_type VARCHAR(100) NOT NULL,
        size_bytes BIGINT NOT NULL,
        bucket_name VARCHAR(100) NOT NULL,
        object_key VARCHAR(500) NOT NULL,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE TABLE IF NOT EXISTS call_sessions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        bubble_id UUID REFERENCES bubbles(id) ON DELETE CASCADE,
        initiator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        participants JSONB DEFAULT '[]',
        call_type VARCHAR(20) NOT NULL,
        status VARCHAR(20) DEFAULT 'initiated',
        started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        ended_at TIMESTAMP WITH TIME ZONE,
        duration_seconds INTEGER DEFAULT 0,
        metadata JSONB DEFAULT '{}'
    );

    CREATE TABLE IF NOT EXISTS bubble_requests (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        request_type bubble_request_type NOT NULL,
        bubble_id UUID REFERENCES bubbles(id) ON DELETE CASCADE,
        requester_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        target_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        status request_status DEFAULT 'pending',
        message TEXT,
        requires_unanimous BOOLEAN DEFAULT true,
        expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        completed_at TIMESTAMP WITH TIME ZONE
    );

    CREATE TABLE IF NOT EXISTS request_votes (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        request_id UUID NOT NULL REFERENCES bubble_requests(id) ON DELETE CASCADE,
        voter_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        vote vote_type NOT NULL,
        voted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(request_id, voter_id)
    );

    CREATE TABLE IF NOT EXISTS friend_requests (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        requester_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        recipient_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        source_bubble_id UUID REFERENCES bubbles(id) ON DELETE SET NULL,
        auto_generated BOOLEAN DEFAULT false,
        status friend_request_status DEFAULT 'pending',
        message TEXT,
        expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        CONSTRAINT friend_requests_different_users CHECK (requester_id != recipient_id)
    );

    CREATE TABLE IF NOT EXISTS roles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(50) UNIQUE NOT NULL,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE TABLE IF NOT EXISTS user_roles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
        is_active BOOLEAN DEFAULT true,
        assigned_by UUID REFERENCES users(id),
        assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, role_id)
    );

    CREATE TABLE IF NOT EXISTS user_relationships (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        from_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        to_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        relationship_type VARCHAR(50) NOT NULL,
        status VARCHAR(20) DEFAULT 'active',
        created_by UUID REFERENCES users(id),
        reason TEXT,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE,
        CONSTRAINT user_relationships_different_users CHECK (from_user_id != to_user_id),
        CONSTRAINT user_relationships_valid_type CHECK (
            relationship_type IN ('ban', 'block', 'mute', 'restrict', 'follow', 'favorite')
        ),
        CONSTRAINT user_relationships_valid_status CHECK (
            status IN ('active', 'inactive', 'expired')
        )
    );

    -- 3. INDEXES
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    CREATE INDEX IF NOT EXISTS idx_users_is_private ON users(is_private);
    CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

    CREATE INDEX IF NOT EXISTS idx_bubbles_creator ON bubbles(creator_id);
    CREATE INDEX IF NOT EXISTS idx_bubbles_status ON bubbles(status);
    CREATE INDEX IF NOT EXISTS idx_bubbles_expires_at ON bubbles(expires_at);
    CREATE INDEX IF NOT EXISTS idx_bubbles_created_at ON bubbles(created_at);
    CREATE INDEX IF NOT EXISTS idx_bubbles_member_count ON bubbles(member_count);

    CREATE INDEX IF NOT EXISTS idx_bubble_members_bubble_id ON bubble_members(bubble_id);
    CREATE INDEX IF NOT EXISTS idx_bubble_members_user_id ON bubble_members(user_id);
    CREATE INDEX IF NOT EXISTS idx_bubble_members_user_id_active ON bubble_members(user_id) WHERE status = 'active';
    CREATE INDEX IF NOT EXISTS idx_bubble_members_status ON bubble_members(status);

    CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_active_member
    ON bubble_members (bubble_id, user_id) WHERE status = 'active';

    CREATE INDEX IF NOT EXISTS idx_notifications_user_id_created_at
    ON notifications (user_id, created_at DESC);

    CREATE INDEX IF NOT EXISTS idx_notifications_user_id_is_read_created_at
    ON notifications (user_id, is_read, created_at DESC);

    CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
    CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

    CREATE INDEX IF NOT EXISTS idx_notifications_grouping_key
    ON notifications(user_id, grouping_key) WHERE grouping_key IS NOT NULL;

    CREATE INDEX IF NOT EXISTS idx_media_files_user_id ON media_files(user_id);
    CREATE INDEX IF NOT EXISTS idx_media_files_content_type ON media_files(content_type);
    CREATE INDEX IF NOT EXISTS idx_media_files_created_at ON media_files(created_at);

    CREATE INDEX IF NOT EXISTS idx_call_sessions_bubble_id ON call_sessions(bubble_id);
    CREATE INDEX IF NOT EXISTS idx_call_sessions_initiator_id ON call_sessions(initiator_id);
    CREATE INDEX IF NOT EXISTS idx_call_sessions_status ON call_sessions(status);
    CREATE INDEX IF NOT EXISTS idx_call_sessions_started_at ON call_sessions(started_at);

    CREATE INDEX IF NOT EXISTS idx_bubble_requests_bubble_id ON bubble_requests(bubble_id);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_requester_id ON bubble_requests(requester_id);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_target_user_id ON bubble_requests(target_user_id);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_status ON bubble_requests(status);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_type ON bubble_requests(request_type);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_expires_at ON bubble_requests(expires_at);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_created_at ON bubble_requests(created_at);

    CREATE INDEX IF NOT EXISTS idx_request_votes_request_id ON request_votes(request_id);
    CREATE INDEX IF NOT EXISTS idx_request_votes_voter_id ON request_votes(voter_id);
    CREATE INDEX IF NOT EXISTS idx_request_votes_vote ON request_votes(vote);
    CREATE INDEX IF NOT EXISTS idx_request_votes_voted_at ON request_votes(voted_at);

    CREATE INDEX IF NOT EXISTS idx_friend_requests_requester_id ON friend_requests(requester_id);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_recipient_id ON friend_requests(recipient_id);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_source_bubble_id ON friend_requests(source_bubble_id);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_status ON friend_requests(status);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_auto_generated ON friend_requests(auto_generated);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_expires_at ON friend_requests(expires_at);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_created_at ON friend_requests(created_at);

    -- 4. TRIGGER FUNCTIONS AND ATTACHMENTS
    CREATE OR REPLACE FUNCTION trigger_set_timestamp()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    CREATE TRIGGER set_timestamp_users
        BEFORE UPDATE ON users
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_bubbles
        BEFORE UPDATE ON bubbles
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_bubble_members
        BEFORE UPDATE ON bubble_members
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_bubble_requests
        BEFORE UPDATE ON bubble_requests
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_friend_requests
        BEFORE UPDATE ON friend_requests
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_roles
        BEFORE UPDATE ON roles
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_user_roles
        BEFORE UPDATE ON user_roles
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_user_relationships
        BEFORE UPDATE ON user_relationships
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE OR REPLACE FUNCTION check_user_bubble_limit()
    RETURNS TRIGGER AS $$
    DECLARE
        user_is_premium BOOLEAN;
        current_bubble_count INTEGER;
        max_allowed_bubbles INTEGER;
    BEGIN
        IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.status != 'active' AND NEW.status = 'active') THEN
            SELECT is_premium INTO user_is_premium
            FROM users
            WHERE id = NEW.user_id;

            IF user_is_premium THEN
                max_allowed_bubbles := 5;  -- Premium users get 5 bubbles
            ELSE
                max_allowed_bubbles := 1;  -- Free users get 1 bubble
            END IF;

            SELECT COUNT(*) INTO current_bubble_count
            FROM bubble_members
            WHERE user_id = NEW.user_id
            AND status = 'active'
            AND bubble_id != NEW.bubble_id; -- Exclude current bubble to handle updates

            IF current_bubble_count >= max_allowed_bubbles THEN
                RAISE EXCEPTION 'User has reached maximum bubble limit. Free users: 1 bubble, Premium users: 5 bubbles. Current: %, Max: %',
                    current_bubble_count, max_allowed_bubbles
                    USING ERRCODE = 'P0001'; -- Custom error code for bubble limit
            END IF;
        END IF;

        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    DROP TRIGGER IF EXISTS enforce_bubble_limit ON bubble_members;
    CREATE TRIGGER enforce_bubble_limit
        BEFORE INSERT OR UPDATE ON bubble_members
        FOR EACH ROW
        EXECUTE FUNCTION check_user_bubble_limit();

    CREATE OR REPLACE FUNCTION update_bubble_member_count_for_log()
    RETURNS TRIGGER AS $$
    BEGIN
        IF TG_OP = 'INSERT' THEN
            IF NEW.status = 'active' THEN
                UPDATE bubbles SET member_count = member_count + 1 WHERE id = NEW.bubble_id;
            END IF;

        ELSIF TG_OP = 'DELETE' THEN
            IF OLD.status = 'active' THEN
                UPDATE bubbles SET member_count = member_count - 1 WHERE id = OLD.bubble_id;
            END IF;

        ELSIF TG_OP = 'UPDATE' THEN
            IF OLD.status = 'active' AND NEW.status <> 'active' THEN
                UPDATE bubbles SET member_count = member_count - 1 WHERE id = OLD.bubble_id;
            ELSIF OLD.status <> 'active' AND NEW.status = 'active' THEN
                UPDATE bubbles SET member_count = member_count + 1 WHERE id = NEW.bubble_id;
            END IF;
        END IF;

        IF (TG_OP = 'DELETE') THEN RETURN OLD; ELSE RETURN NEW; END IF;
    END;
    $$ LANGUAGE plpgsql;

    DROP TRIGGER IF EXISTS sync_member_count_log ON bubble_members;
    CREATE TRIGGER sync_member_count_log
        AFTER INSERT OR UPDATE OR DELETE ON bubble_members
        FOR EACH ROW
        EXECUTE FUNCTION update_bubble_member_count_for_log();

    UPDATE bubbles
    SET member_count = (
        SELECT COUNT(*)
        FROM bubble_members
        WHERE bubble_members.bubble_id = bubbles.id
        AND bubble_members.status = 'active'
    );

    COMMIT;
    `
    if _, err := c.Pool.Exec(ctx, fullSchema); err != nil {
        return fmt.Errorf("failed to execute schema transaction: %w", err)
    }

    c.logger.Info("PostgreSQL schema initialized successfully")
    return nil
}
// ... (rest of the file remains the same)
