package database

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// Example DAL implementation showing best practices for structured error handling

// User represents a user entity
type User struct {
	ID                   string                 `json:"id"`
	Username             string                 `json:"username"`
	Email                string                 `json:"email"`
	FirstName            string                 `json:"first_name"`
	LastName             string                 `json:"last_name"`
	DisplayName          string                 `json:"display_name"`
	AvatarBucketName     *string                `json:"avatar_bucket_name,omitempty"`
	AvatarObjectKey      *string                `json:"avatar_object_key,omitempty"`
	DateOfBirth          time.Time              `json:"date_of_birth"`
	IsPremium            bool                   `json:"is_premium"`
	IsActive             bool                   `json:"is_active"`
	IsPrivate            bool                   `json:"is_private"`
	LastActiveAt         time.Time              `json:"last_active_at"`
	PresenceStatus       string                 `json:"presence_status"`
	NotificationSettings map[string]interface{} `json:"notification_settings"`
	CreatedAt            time.Time              `json:"created_at"`
	UpdatedAt            time.Time              `json:"updated_at"`
}

// UserRepository defines the interface for user data access
type UserRepository interface {
	CreateUser(ctx context.Context, user *User) error
	GetUserByID(ctx context.Context, userID string) (*User, error)
	GetUserByEmail(ctx context.Context, email string) (*User, error)
	GetUserByUsername(ctx context.Context, username string) (*User, error)
	UpdateUser(ctx context.Context, user *User) error
	DeleteUser(ctx context.Context, userID string) error
}

// PostgreSQLUserRepository implements UserRepository using PostgreSQL
type PostgreSQLUserRepository struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLUserRepository creates a new PostgreSQL user repository
func NewPostgreSQLUserRepository(pool *pgxpool.Pool, logger *zap.Logger) UserRepository {
	return &PostgreSQLUserRepository{
		pool:   pool,
		logger: logger,
	}
}

// CreateUser creates a new user with proper error handling
func (r *PostgreSQLUserRepository) CreateUser(ctx context.Context, user *User) error {
	// Generate ID if not provided
	if user.ID == "" {
		user.ID = uuid.New().String()
	}

	// Set timestamps
	now := time.Now()
	user.CreatedAt = now
	user.UpdatedAt = now

	query := `
		INSERT INTO users (
			id, username, email, first_name, last_name, display_name,
			avatar_bucket_name, avatar_object_key, date_of_birth,
			is_premium, is_active, is_private, last_active_at,
			presence_status, notification_settings, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
		)`

	_, err := r.pool.Exec(ctx, query,
		user.ID, user.Username, user.Email, user.FirstName, user.LastName,
		user.DisplayName, user.AvatarBucketName, user.AvatarObjectKey,
		user.DateOfBirth, user.IsPremium, user.IsActive, user.IsPrivate,
		user.LastActiveAt, user.PresenceStatus, user.NotificationSettings,
		user.CreatedAt, user.UpdatedAt,
	)

	if err != nil {
		// Use structured error handling - this will automatically detect
		// unique constraint violations, foreign key violations, etc.
		return fmt.Errorf("failed to create user: %w", HandlePgxError(err, "users"))
	}

	r.logger.Info("User created successfully", zap.String("user_id", user.ID))
	return nil
}

// GetUserByID retrieves a user by ID with proper error handling
func (r *PostgreSQLUserRepository) GetUserByID(ctx context.Context, userID string) (*User, error) {
	query := `
		SELECT id, username, email, first_name, last_name, display_name,
		       avatar_bucket_name, avatar_object_key, date_of_birth,
		       is_premium, is_active, is_private, last_active_at,
		       presence_status, notification_settings, created_at, updated_at
		FROM users
		WHERE id = $1 AND is_active = true`

	var user User
	err := r.pool.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
		&user.DisplayName, &user.AvatarBucketName, &user.AvatarObjectKey,
		&user.DateOfBirth, &user.IsPremium, &user.IsActive, &user.IsPrivate,
		&user.LastActiveAt, &user.PresenceStatus, &user.NotificationSettings,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		// HandlePgxError will convert pgx.ErrNoRows to NotFoundError
		return nil, fmt.Errorf("failed to get user by ID: %w", HandlePgxError(err, "users"))
	}

	return &user, nil
}

// GetUserByEmail retrieves a user by email with proper error handling
func (r *PostgreSQLUserRepository) GetUserByEmail(ctx context.Context, email string) (*User, error) {
	query := `
		SELECT id, username, email, first_name, last_name, display_name,
		       avatar_bucket_name, avatar_object_key, date_of_birth,
		       is_premium, is_active, is_private, last_active_at,
		       presence_status, notification_settings, created_at, updated_at
		FROM users
		WHERE LOWER(email) = LOWER($1) AND is_active = true`

	var user User
	err := r.pool.QueryRow(ctx, query, email).Scan(
		&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
		&user.DisplayName, &user.AvatarBucketName, &user.AvatarObjectKey,
		&user.DateOfBirth, &user.IsPremium, &user.IsActive, &user.IsPrivate,
		&user.LastActiveAt, &user.PresenceStatus, &user.NotificationSettings,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user by email: %w", HandlePgxError(err, "users"))
	}

	return &user, nil
}

// GetUserByUsername retrieves a user by username with proper error handling
func (r *PostgreSQLUserRepository) GetUserByUsername(ctx context.Context, username string) (*User, error) {
	query := `
		SELECT id, username, email, first_name, last_name, display_name,
		       avatar_bucket_name, avatar_object_key, date_of_birth,
		       is_premium, is_active, is_private, last_active_at,
		       presence_status, notification_settings, created_at, updated_at
		FROM users
		WHERE LOWER(username) = LOWER($1) AND is_active = true`

	var user User
	err := r.pool.QueryRow(ctx, query, username).Scan(
		&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
		&user.DisplayName, &user.AvatarBucketName, &user.AvatarObjectKey,
		&user.DateOfBirth, &user.IsPremium, &user.IsActive, &user.IsPrivate,
		&user.LastActiveAt, &user.PresenceStatus, &user.NotificationSettings,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user by username: %w", HandlePgxError(err, "users"))
	}

	return &user, nil
}

// UpdateUser updates an existing user with proper error handling
func (r *PostgreSQLUserRepository) UpdateUser(ctx context.Context, user *User) error {
	// Update timestamp
	user.UpdatedAt = time.Now()

	query := `
		UPDATE users SET
			username = $2, email = $3, first_name = $4, last_name = $5,
			display_name = $6, avatar_bucket_name = $7, avatar_object_key = $8,
			date_of_birth = $9, is_premium = $10, is_active = $11, is_private = $12,
			last_active_at = $13, presence_status = $14, notification_settings = $15,
			updated_at = $16
		WHERE id = $1`

	result, err := r.pool.Exec(ctx, query,
		user.ID, user.Username, user.Email, user.FirstName, user.LastName,
		user.DisplayName, user.AvatarBucketName, user.AvatarObjectKey,
		user.DateOfBirth, user.IsPremium, user.IsActive, user.IsPrivate,
		user.LastActiveAt, user.PresenceStatus, user.NotificationSettings,
		user.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update user: %w", HandlePgxError(err, "users"))
	}

	// Check if any rows were affected
	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to update user: %w", NewNotFoundError("users", "user not found"))
	}

	r.logger.Info("User updated successfully", zap.String("user_id", user.ID))
	return nil
}

// DeleteUser soft deletes a user with proper error handling
func (r *PostgreSQLUserRepository) DeleteUser(ctx context.Context, userID string) error {
	query := `UPDATE users SET is_active = false, updated_at = NOW() WHERE id = $1`

	result, err := r.pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to delete user: %w", HandlePgxError(err, "users"))
	}

	// Check if any rows were affected
	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to delete user: %w", NewNotFoundError("users", "user not found"))
	}

	r.logger.Info("User deleted successfully", zap.String("user_id", userID))
	return nil
}
