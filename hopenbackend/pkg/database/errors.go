package database

import (
	"errors"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
)

// Structured error variables for common database operations
var (
	// Record not found errors
	ErrNotFound      = errors.New("record not found")
	ErrUserNotFound  = errors.New("user not found")
	ErrBubbleNotFound = errors.New("bubble not found")
	ErrContactNotFound = errors.New("contact not found")
	ErrFriendshipNotFound = errors.New("friendship not found")
	ErrNotificationNotFound = errors.New("notification not found")
	ErrMediaFileNotFound = errors.New("media file not found")

	// Duplicate/constraint violation errors
	ErrDuplicate         = errors.New("record already exists")
	ErrUserExists        = errors.New("user already exists")
	ErrEmailExists       = errors.New("email already exists")
	ErrUsernameExists    = errors.New("username already exists")
	ErrFriendshipExists  = errors.New("friendship already exists")
	ErrContactExists     = errors.New("contact request already exists")

	// Business logic errors
	ErrBubbleLimit       = errors.New("user has reached their bubble limit")
	ErrBubbleFull        = errors.New("bubble has reached maximum capacity")
	ErrInvalidStatus     = errors.New("invalid status for operation")
	ErrPermissionDenied  = errors.New("permission denied")
	ErrInvalidInput      = errors.New("invalid input data")

	// Connection and transaction errors
	ErrConnectionFailed  = errors.New("database connection failed")
	ErrTransactionFailed = errors.New("database transaction failed")
	ErrQueryFailed       = errors.New("database query failed")
)

// PostgreSQL error codes
const (
	// Constraint violations
	PgUniqueViolation    = "23505"
	PgForeignKeyViolation = "23503"
	PgCheckViolation     = "23514"
	PgNotNullViolation   = "23502"

	// Custom error codes (defined in triggers)
	PgBubbleLimitError   = "P0001"
)

// ErrorType represents the category of database error
type ErrorType int

const (
	ErrorTypeNotFound ErrorType = iota
	ErrorTypeDuplicate
	ErrorTypeConstraint
	ErrorTypeBusiness
	ErrorTypeConnection
	ErrorTypeUnknown
)

// DatabaseError provides structured error information
type DatabaseError struct {
	Type     ErrorType
	Code     string
	Message  string
	Original error
	Table    string
	Column   string
}

func (e *DatabaseError) Error() string {
	if e.Original != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Original)
	}
	return e.Message
}

func (e *DatabaseError) Unwrap() error {
	return e.Original
}

// Is implements error matching for errors.Is()
func (e *DatabaseError) Is(target error) bool {
	if target == nil {
		return false
	}

	// Check against our structured error variables
	switch target {
	case ErrNotFound:
		return e.Type == ErrorTypeNotFound
	case ErrDuplicate:
		return e.Type == ErrorTypeDuplicate
	case ErrBubbleLimit:
		return e.Code == PgBubbleLimitError
	case ErrUserNotFound:
		return e.Type == ErrorTypeNotFound && e.Table == "users"
	case ErrBubbleNotFound:
		return e.Type == ErrorTypeNotFound && e.Table == "bubbles"
	case ErrContactNotFound:
		return e.Type == ErrorTypeNotFound && e.Table == "contacts"
	case ErrFriendshipNotFound:
		return e.Type == ErrorTypeNotFound && e.Table == "friendships"
	case ErrEmailExists:
		return e.Type == ErrorTypeDuplicate && e.Column == "email"
	case ErrUsernameExists:
		return e.Type == ErrorTypeDuplicate && e.Column == "username"
	}

	return false
}

// HandlePgxError converts pgx errors to structured database errors
func HandlePgxError(err error, table string) error {
	if err == nil {
		return nil
	}

	// Handle pgx.ErrNoRows
	if errors.Is(err, pgx.ErrNoRows) {
		return &DatabaseError{
			Type:     ErrorTypeNotFound,
			Message:  getNotFoundMessage(table),
			Original: err,
			Table:    table,
		}
	}

	// Handle PostgreSQL errors
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return handlePgError(pgErr, table)
	}

	// Handle other database errors
	return &DatabaseError{
		Type:     ErrorTypeUnknown,
		Message:  "database operation failed",
		Original: err,
		Table:    table,
	}
}

// handlePgError converts PostgreSQL errors to structured errors
func handlePgError(pgErr *pgconn.PgError, table string) error {
	switch pgErr.Code {
	case PgUniqueViolation:
		return handleUniqueViolation(pgErr, table)
	case PgForeignKeyViolation:
		return &DatabaseError{
			Type:     ErrorTypeConstraint,
			Code:     pgErr.Code,
			Message:  "foreign key constraint violation",
			Original: pgErr,
			Table:    table,
		}
	case PgCheckViolation:
		return &DatabaseError{
			Type:     ErrorTypeConstraint,
			Code:     pgErr.Code,
			Message:  "check constraint violation",
			Original: pgErr,
			Table:    table,
		}
	case PgNotNullViolation:
		return &DatabaseError{
			Type:     ErrorTypeConstraint,
			Code:     pgErr.Code,
			Message:  "not null constraint violation",
			Original: pgErr,
			Table:    table,
			Column:   pgErr.ColumnName,
		}
	case PgBubbleLimitError:
		return &DatabaseError{
			Type:     ErrorTypeBusiness,
			Code:     pgErr.Code,
			Message:  "user has reached their bubble limit",
			Original: pgErr,
			Table:    table,
		}
	default:
		return &DatabaseError{
			Type:     ErrorTypeUnknown,
			Code:     pgErr.Code,
			Message:  pgErr.Message,
			Original: pgErr,
			Table:    table,
		}
	}
}

// handleUniqueViolation provides specific error messages for unique constraint violations
func handleUniqueViolation(pgErr *pgconn.PgError, table string) error {
	constraintName := pgErr.ConstraintName
	
	// Map constraint names to user-friendly errors
	switch {
	case constraintName == "users_email_key":
		return &DatabaseError{
			Type:     ErrorTypeDuplicate,
			Code:     pgErr.Code,
			Message:  "email already exists",
			Original: pgErr,
			Table:    table,
			Column:   "email",
		}
	case constraintName == "users_username_key":
		return &DatabaseError{
			Type:     ErrorTypeDuplicate,
			Code:     pgErr.Code,
			Message:  "username already exists",
			Original: pgErr,
			Table:    table,
			Column:   "username",
		}
	default:
		return &DatabaseError{
			Type:     ErrorTypeDuplicate,
			Code:     pgErr.Code,
			Message:  "record already exists",
			Original: pgErr,
			Table:    table,
		}
	}
}

// getNotFoundMessage returns appropriate not found message for table
func getNotFoundMessage(table string) string {
	switch table {
	case "users":
		return "user not found"
	case "bubbles":
		return "bubble not found"
	case "contacts":
		return "contact not found"
	case "friendships":
		return "friendship not found"
	case "notifications":
		return "notification not found"
	case "media_files":
		return "media file not found"
	default:
		return "record not found"
	}
}

// IsNotFound checks if error is a not found error
func IsNotFound(err error) bool {
	return errors.Is(err, ErrNotFound)
}

// IsDuplicate checks if error is a duplicate record error
func IsDuplicate(err error) bool {
	return errors.Is(err, ErrDuplicate)
}

// IsBubbleLimit checks if error is a bubble limit error
func IsBubbleLimit(err error) bool {
	return errors.Is(err, ErrBubbleLimit)
}

// IsConstraintViolation checks if error is any constraint violation
func IsConstraintViolation(err error) bool {
	var dbErr *DatabaseError
	if errors.As(err, &dbErr) {
		return dbErr.Type == ErrorTypeConstraint
	}
	return false
}
