package database

import (
	"errors"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
)

// Error interfaces for behavior-based error checking (Go best practice)
type NotFoundError interface {
	error
	NotFound() bool
}

type DuplicateError interface {
	error
	Duplicate() bool
}

type BubbleLimitError interface {
	error
	BubbleLimit() bool
}

type ConstraintError interface {
	error
	Constraint() bool
}

// Base error types that implement the behavior interfaces
type notFoundError struct {
	message string
	table   string
}

func (e *notFoundError) Error() string { return e.message }
func (e *notFoundError) NotFound() bool { return true }

type duplicateError struct {
	message string
	table   string
	column  string
}

func (e *duplicateError) Error() string { return e.message }
func (e *duplicateError) Duplicate() bool { return true }

type bubbleLimitError struct {
	message string
}

func (e *bubbleLimitError) Error() string { return e.message }
func (e *bubbleLimitError) BubbleLimit() bool { return true }

type constraintError struct {
	message string
	code    string
}

func (e *constraintError) Error() string { return e.message }
func (e *constraintError) Constraint() bool { return true }

// PostgreSQL error codes
const (
	// Constraint violations
	PgUniqueViolation     = "23505"
	PgForeignKeyViolation = "23503"
	PgCheckViolation      = "23514"
	PgNotNullViolation    = "23502"

	// Custom error codes (defined in triggers)
	PgBubbleLimitError    = "P0001"
)

// Constructor functions for creating specific error types
func NewNotFoundError(table, message string) error {
	if message == "" {
		message = getNotFoundMessage(table)
	}
	return &notFoundError{
		message: message,
		table:   table,
	}
}

func NewDuplicateError(table, column, message string) error {
	if message == "" {
		message = getDuplicateMessage(table, column)
	}
	return &duplicateError{
		message: message,
		table:   table,
		column:  column,
	}
}

func NewBubbleLimitError(message string) error {
	if message == "" {
		message = "user has reached their bubble limit"
	}
	return &bubbleLimitError{
		message: message,
	}
}

func NewConstraintError(code, message string) error {
	return &constraintError{
		message: message,
		code:    code,
	}
}

// HandlePgxError converts pgx errors to structured database errors using Go 1.13+ best practices
func HandlePgxError(err error, table string) error {
	if err == nil {
		return nil
	}

	// Handle pgx.ErrNoRows - return behavior-based error
	if errors.Is(err, pgx.ErrNoRows) {
		return fmt.Errorf("database query failed: %w",
			NewNotFoundError(table, ""))
	}

	// Handle PostgreSQL errors
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return fmt.Errorf("database operation failed: %w",
			handlePgError(pgErr, table))
	}

	// Handle other database errors - wrap with context
	return fmt.Errorf("database operation failed for table %s: %w", table, err)
}

// handlePgError converts PostgreSQL errors to behavior-based errors
func handlePgError(pgErr *pgconn.PgError, table string) error {
	switch pgErr.Code {
	case PgUniqueViolation:
		return handleUniqueViolation(pgErr, table)
	case PgForeignKeyViolation:
		return NewConstraintError(pgErr.Code, "foreign key constraint violation")
	case PgCheckViolation:
		return NewConstraintError(pgErr.Code, "check constraint violation")
	case PgNotNullViolation:
		return NewConstraintError(pgErr.Code, "not null constraint violation")
	case PgBubbleLimitError:
		return NewBubbleLimitError("")
	default:
		return NewConstraintError(pgErr.Code, pgErr.Message)
	}
}

// handleUniqueViolation provides specific error messages for unique constraint violations
func handleUniqueViolation(pgErr *pgconn.PgError, table string) error {
	constraintName := pgErr.ConstraintName

	// Map constraint names to user-friendly errors
	switch {
	case constraintName == "users_email_key":
		return NewDuplicateError(table, "email", "email already exists")
	case constraintName == "users_username_key":
		return NewDuplicateError(table, "username", "username already exists")
	default:
		return NewDuplicateError(table, "", "record already exists")
	}
}

// Helper functions for generating error messages
func getNotFoundMessage(table string) string {
	switch table {
	case "users":
		return "user not found"
	case "bubbles":
		return "bubble not found"
	case "contacts":
		return "contact not found"
	case "friendships":
		return "friendship not found"
	case "notifications":
		return "notification not found"
	case "media_files":
		return "media file not found"
	default:
		return "record not found"
	}
}

func getDuplicateMessage(table, column string) string {
	switch column {
	case "email":
		return "email already exists"
	case "username":
		return "username already exists"
	default:
		return "record already exists"
	}
}

// Behavior-based error checking functions (Go best practice)
func IsNotFound(err error) bool {
	var nfErr NotFoundError
	return errors.As(err, &nfErr) && nfErr.NotFound()
}

func IsDuplicate(err error) bool {
	var dupErr DuplicateError
	return errors.As(err, &dupErr) && dupErr.Duplicate()
}

func IsBubbleLimit(err error) bool {
	var blErr BubbleLimitError
	return errors.As(err, &blErr) && blErr.BubbleLimit()
}

func IsConstraint(err error) bool {
	var cErr ConstraintError
	return errors.As(err, &cErr) && cErr.Constraint()
}
