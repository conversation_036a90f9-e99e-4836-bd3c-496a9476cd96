package database

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Example service showing how to use structured error handling in HTTP handlers

// UserService provides HTTP handlers for user operations
type UserService struct {
	userRepo UserRepository
	logger   *zap.Logger
}

// NewUserService creates a new user service
func NewUserService(userRepo UserRepository, logger *zap.Logger) *UserService {
	return &UserService{
		userRepo: userRepo,
		logger:   logger,
	}
}

// CreateUserRequest represents the request payload for creating a user
type CreateUserRequest struct {
	Username    string    `json:"username" binding:"required,min=3,max=40"`
	Email       string    `json:"email" binding:"required,email"`
	FirstName   string    `json:"first_name" binding:"required,max=100"`
	LastName    string    `json:"last_name" binding:"required,max=100"`
	DisplayName string    `json:"display_name" binding:"required,max=200"`
	DateOfBirth time.Time `json:"date_of_birth" binding:"required"`
	IsPrivate   bool      `json:"is_private"`
}

// UserResponse represents the response payload for user operations
type UserResponse struct {
	ID              string    `json:"id"`
	Username        string    `json:"username"`
	Email           string    `json:"email"`
	FirstName       string    `json:"first_name"`
	LastName        string    `json:"last_name"`
	DisplayName     string    `json:"display_name"`
	DateOfBirth     time.Time `json:"date_of_birth"`
	IsPremium       bool      `json:"is_premium"`
	IsPrivate       bool      `json:"is_private"`
	PresenceStatus  string    `json:"presence_status"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// CreateUser handles user creation with proper error handling
func (s *UserService) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusBadRequest, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.1",
			Title:  "Bad Request",
			Status: http.StatusBadRequest,
			Detail: "Invalid request payload: " + err.Error(),
		})
		return
	}

	// Create user entity
	user := &User{
		Username:             req.Username,
		Email:                req.Email,
		FirstName:            req.FirstName,
		LastName:             req.LastName,
		DisplayName:          req.DisplayName,
		DateOfBirth:          req.DateOfBirth,
		IsPremium:            false, // Default to free user
		IsActive:             true,
		IsPrivate:            req.IsPrivate,
		LastActiveAt:         time.Now(),
		PresenceStatus:       "offline",
		NotificationSettings: getDefaultNotificationSettings(),
	}

	// Attempt to create user
	err := s.userRepo.CreateUser(c.Request.Context(), user)
	if err != nil {
		// Use structured error handling - this will automatically
		// return appropriate HTTP status codes and RFC 7807 responses
		HandleUserError(c, err, "create user")
		return
	}

	// Return success response
	response := &UserResponse{
		ID:             user.ID,
		Username:       user.Username,
		Email:          user.Email,
		FirstName:      user.FirstName,
		LastName:       user.LastName,
		DisplayName:    user.DisplayName,
		DateOfBirth:    user.DateOfBirth,
		IsPremium:      user.IsPremium,
		IsPrivate:      user.IsPrivate,
		PresenceStatus: user.PresenceStatus,
		CreatedAt:      user.CreatedAt,
		UpdatedAt:      user.UpdatedAt,
	}

	c.JSON(http.StatusCreated, response)
}

// GetUser handles user retrieval with proper error handling
func (s *UserService) GetUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusBadRequest, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.1",
			Title:  "Bad Request",
			Status: http.StatusBadRequest,
			Detail: "User ID is required",
		})
		return
	}

	// Attempt to get user
	user, err := s.userRepo.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		// Use structured error handling
		HandleUserError(c, err, "get user")
		return
	}

	// Return success response
	response := &UserResponse{
		ID:             user.ID,
		Username:       user.Username,
		Email:          user.Email,
		FirstName:      user.FirstName,
		LastName:       user.LastName,
		DisplayName:    user.DisplayName,
		DateOfBirth:    user.DateOfBirth,
		IsPremium:      user.IsPremium,
		IsPrivate:      user.IsPrivate,
		PresenceStatus: user.PresenceStatus,
		CreatedAt:      user.CreatedAt,
		UpdatedAt:      user.UpdatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// UpdateUser handles user updates with proper error handling
func (s *UserService) UpdateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusBadRequest, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.1",
			Title:  "Bad Request",
			Status: http.StatusBadRequest,
			Detail: "User ID is required",
		})
		return
	}

	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusBadRequest, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.1",
			Title:  "Bad Request",
			Status: http.StatusBadRequest,
			Detail: "Invalid request payload: " + err.Error(),
		})
		return
	}

	// Get existing user first
	existingUser, err := s.userRepo.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		HandleUserError(c, err, "get user for update")
		return
	}

	// Update user fields
	existingUser.Username = req.Username
	existingUser.Email = req.Email
	existingUser.FirstName = req.FirstName
	existingUser.LastName = req.LastName
	existingUser.DisplayName = req.DisplayName
	existingUser.DateOfBirth = req.DateOfBirth
	existingUser.IsPrivate = req.IsPrivate

	// Attempt to update user
	err = s.userRepo.UpdateUser(c.Request.Context(), existingUser)
	if err != nil {
		HandleUserError(c, err, "update user")
		return
	}

	// Return success response
	response := &UserResponse{
		ID:             existingUser.ID,
		Username:       existingUser.Username,
		Email:          existingUser.Email,
		FirstName:      existingUser.FirstName,
		LastName:       existingUser.LastName,
		DisplayName:    existingUser.DisplayName,
		DateOfBirth:    existingUser.DateOfBirth,
		IsPremium:      existingUser.IsPremium,
		IsPrivate:      existingUser.IsPrivate,
		PresenceStatus: existingUser.PresenceStatus,
		CreatedAt:      existingUser.CreatedAt,
		UpdatedAt:      existingUser.UpdatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// DeleteUser handles user deletion with proper error handling
func (s *UserService) DeleteUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusBadRequest, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.1",
			Title:  "Bad Request",
			Status: http.StatusBadRequest,
			Detail: "User ID is required",
		})
		return
	}

	// Attempt to delete user
	err := s.userRepo.DeleteUser(c.Request.Context(), userID)
	if err != nil {
		HandleUserError(c, err, "delete user")
		return
	}

	// Return success response (204 No Content)
	c.Status(http.StatusNoContent)
}

// Helper function to get default notification settings
func getDefaultNotificationSettings() map[string]interface{} {
	return map[string]interface{}{
		"birthday": map[string]interface{}{
			"email":   true,
			"enabled": true,
		},
		"bubble": map[string]interface{}{
			"email":   true,
			"enabled": true,
		},
		"friendship": map[string]interface{}{
			"email":   true,
			"enabled": true,
		},
		"contact": map[string]interface{}{
			"email":   true,
			"enabled": true,
		},
	}
}
