-- Test script for the Hybrid Bubble Badge System
-- This script tests both the immutable log functionality and the performance optimization

-- Test Setup: Create test data
DO $$
DECLARE
    test_bubble_id UUID;
    test_user1_id UUID := '550e8400-e29b-41d4-a716-************';
    test_user2_id UUID := '550e8400-e29b-41d4-a716-************';
    test_user3_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Clean up any existing test data
    DELETE FROM bubble_members WHERE user_id IN (test_user1_id, test_user2_id, test_user3_id);
    DELETE FROM bubbles WHERE name = 'Test Bubble for Hybrid System';
    DELETE FROM users WHERE id IN (test_user1_id, test_user2_id, test_user3_id);

    -- Create test users
    INSERT INTO users (id, username, email, first_name, last_name, display_name, date_of_birth)
    VALUES 
        (test_user1_id, 'testuser1', '<EMAIL>', 'Test', 'User1', 'Test User 1', '1990-01-01'),
        (test_user2_id, 'testuser2', '<EMAIL>', 'Test', 'User2', 'Test User 2', '1990-01-02'),
        (test_user3_id, 'testuser3', '<EMAIL>', 'Test', 'User3', 'Test User 3', '1990-01-03');

    -- Create test bubble with capacity 3
    INSERT INTO bubbles (name, capacity, creator_id)
    VALUES ('Test Bubble for Hybrid System', 3, test_user1_id)
    RETURNING id INTO test_bubble_id;

    RAISE NOTICE 'Test setup complete. Bubble ID: %', test_bubble_id;
END $$;

-- Test 1: Verify initial state
SELECT 'Test 1: Initial State' as test_name;
SELECT 
    b.name,
    b.capacity,
    b.member_count,
    'Should be 0' as expected_member_count
FROM bubbles b 
WHERE b.name = 'Test Bubble for Hybrid System';

-- Test 2: User 1 joins (creator automatically active)
INSERT INTO bubble_members (bubble_id, user_id, status, joined_at)
SELECT id, creator_id, 'active', NOW()
FROM bubbles 
WHERE name = 'Test Bubble for Hybrid System';

SELECT 'Test 2: Creator joins' as test_name;
SELECT 
    b.name,
    b.member_count,
    'Should be 1' as expected_member_count
FROM bubbles b 
WHERE b.name = 'Test Bubble for Hybrid System';

-- Test 3: User 2 gets invited (pending status)
INSERT INTO bubble_members (bubble_id, user_id, status)
SELECT b.id, '550e8400-e29b-41d4-a716-************', 'pending'
FROM bubbles b 
WHERE b.name = 'Test Bubble for Hybrid System';

SELECT 'Test 3: User 2 invited (pending)' as test_name;
SELECT 
    b.name,
    b.member_count,
    'Should still be 1' as expected_member_count
FROM bubbles b 
WHERE b.name = 'Test Bubble for Hybrid System';

-- Test 4: User 2 accepts invitation (pending -> active)
UPDATE bubble_members 
SET status = 'active', joined_at = NOW()
WHERE user_id = '550e8400-e29b-41d4-a716-************' 
AND status = 'pending';

SELECT 'Test 4: User 2 accepts (pending -> active)' as test_name;
SELECT 
    b.name,
    b.member_count,
    'Should be 2' as expected_member_count
FROM bubbles b 
WHERE b.name = 'Test Bubble for Hybrid System';

-- Test 5: User 3 joins directly (INSERT active)
INSERT INTO bubble_members (bubble_id, user_id, status, joined_at)
SELECT b.id, '550e8400-e29b-41d4-a716-************', 'active', NOW()
FROM bubbles b 
WHERE b.name = 'Test Bubble for Hybrid System';

SELECT 'Test 5: User 3 joins directly (INSERT active)' as test_name;
SELECT 
    b.name,
    b.member_count,
    'Should be 3 (FULL!)' as expected_member_count
FROM bubbles b 
WHERE b.name = 'Test Bubble for Hybrid System';

-- Test 6: Test the bubble badge query with full bubble
SELECT 'Test 6: Bubble badge status with full bubble' as test_name;
SELECT
    u.id AS user_id,
    u.username,
    CASE
        WHEN b.id IS NULL THEN 'no_bubble'
        WHEN b.member_count = b.capacity THEN 'bubble_full'
        ELSE 'in_a_bubble'
    END AS bubble_badge_status
FROM users u
LEFT JOIN bubble_members bm ON u.id = bm.user_id AND bm.status = 'active'
LEFT JOIN bubbles b ON bm.bubble_id = b.id AND b.status = 'active'
WHERE u.username LIKE 'testuser%'
ORDER BY u.username;

-- Test 7: User 2 leaves (active -> left)
UPDATE bubble_members 
SET status = 'left', left_at = NOW()
WHERE user_id = '550e8400-e29b-41d4-a716-************' 
AND status = 'active';

SELECT 'Test 7: User 2 leaves (active -> left)' as test_name;
SELECT 
    b.name,
    b.member_count,
    'Should be 2' as expected_member_count
FROM bubbles b 
WHERE b.name = 'Test Bubble for Hybrid System';

-- Test 8: Test the bubble badge query with non-full bubble
SELECT 'Test 8: Bubble badge status with non-full bubble' as test_name;
SELECT
    u.id AS user_id,
    u.username,
    CASE
        WHEN b.id IS NULL THEN 'no_bubble'
        WHEN b.member_count = b.capacity THEN 'bubble_full'
        ELSE 'in_a_bubble'
    END AS bubble_badge_status
FROM users u
LEFT JOIN bubble_members bm ON u.id = bm.user_id AND bm.status = 'active'
LEFT JOIN bubbles b ON bm.bubble_id = b.id AND b.status = 'active'
WHERE u.username LIKE 'testuser%'
ORDER BY u.username;

-- Test 9: Verify the immutable log (analytics capability)
SELECT 'Test 9: Complete membership history (Analytics)' as test_name;
SELECT 
    u.username,
    bm.status,
    bm.joined_at,
    bm.left_at,
    bm.created_at
FROM bubble_members bm
JOIN users u ON bm.user_id = u.id
WHERE u.username LIKE 'testuser%'
ORDER BY u.username, bm.created_at;

-- Test 10: Verify partial unique index works (should prevent duplicate active records)
SELECT 'Test 10: Testing partial unique index (should fail)' as test_name;
DO $$
BEGIN
    -- This should fail due to the partial unique index
    INSERT INTO bubble_members (bubble_id, user_id, status)
    SELECT b.id, '550e8400-e29b-41d4-a716-************', 'active'
    FROM bubbles b 
    WHERE b.name = 'Test Bubble for Hybrid System';
    
    RAISE NOTICE 'ERROR: Partial unique index failed to prevent duplicate active record!';
EXCEPTION
    WHEN unique_violation THEN
        RAISE NOTICE 'SUCCESS: Partial unique index correctly prevented duplicate active record';
END $$;

-- Cleanup
DELETE FROM bubble_members WHERE user_id IN (
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************', 
    '550e8400-e29b-41d4-a716-************'
);
DELETE FROM bubbles WHERE name = 'Test Bubble for Hybrid System';
DELETE FROM users WHERE username LIKE 'testuser%';

SELECT 'All tests completed! Check the results above.' as final_message;
