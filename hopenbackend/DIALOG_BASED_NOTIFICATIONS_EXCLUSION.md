# Dialog-Based Notifications Exclusion Strategy

## Overview
This document explains why certain notification types are excluded from the PostgreSQL notifications schema to prevent duplicate user notifications when dedicated dialogs are already shown.

## Problem Statement
Some notification events trigger dedicated dialog widgets that immediately show the user relevant information and action buttons. If these same events also create entries in the notifications list, users would see duplicate notifications for the same event, creating a poor user experience.

## Excluded Notification Types

### 1. **Contact Request Received** ❌
- **Type**: `contactRequestReceived`
- **Dialog**: `ContactRequestDialog`
- **Location**: `/Users/<USER>/Hopen/hopen/lib/presentation/widgets/requests/contact_request_dialog.dart`
- **Reason**: User immediately sees dialog when someone sends a contact request
- **User Experience**: Dialog provides accept/decline actions, no need for notification list entry

### 2. **Bubble Invitation Received** ❌
- **Type**: `bubbleInvitationReceived`
- **Dialog**: `BubbleInviteRequestDialog`
- **Location**: `/Users/<USER>/Hopen/hopen/lib/presentation/widgets/requests/bubble_invite_request_dialog.dart`
- **Reason**: User immediately sees dialog when invited to join a bubble
- **User Experience**: Dialog provides accept/decline actions, no need for notification list entry

### 3. **Bubble Join Request Received** ❌
- **Type**: `bubbleJoinRequestReceived`
- **Dialog**: `BubbleJoinRequestDialog`
- **Location**: `/Users/<USER>/Hopen/hopen/lib/presentation/widgets/requests/bubble_join_request_dialog.dart`
- **Reason**: User immediately sees dialog when someone wants to join their bubble
- **User Experience**: Dialog provides accept/decline actions, no need for notification list entry

### 4. **Bubble Member Joined** ❌
- **Type**: `bubbleMemberJoined`
- **Dialog**: `BubbleMemberJoinedDialog` (to be created)
- **Location**: `/Users/<USER>/Hopen/hopen/lib/presentation/widgets/requests/bubble_member_joined_dialog.dart`
- **Reason**: User will immediately see dialog when someone joins their bubble
- **User Experience**: Dialog provides acknowledgment, no need for notification list entry

### 5. **Bubble Votekick Initiated** ❌
- **Type**: `bubbleVotekickInitiated`
- **Dialog**: `BubbleKickoutRequestDialog`
- **Location**: `/Users/<USER>/Hopen/hopen/lib/presentation/widgets/requests/bubble_kickout_request_dialog.dart`
- **Reason**: User immediately sees dialog when a member requests to kick out another member
- **User Experience**: Dialog provides voting actions, no need for notification list entry

### 6. **Bubble Join Request Accepted** ❌
- **Type**: `bubbleJoinRequestAccepted`
- **Dialog**: `BubbleMemberJoinedDialog`
- **Location**: `/Users/<USER>/Hopen/hopen/lib/presentation/widgets/requests/bubble_member_joined_dialog.dart`
- **Reason**: User will see dialog when someone joins their bubble (same as bubbleMemberJoined)
- **User Experience**: Dialog shows new member profile with "OK" button, no need for notification list entry

### 7. **Friendship Established** ❌
- **Type**: `friendshipEstablished`
- **Dialog**: `FriendshipEstablishedDialog`
- **Location**: `/Users/<USER>/Hopen/hopen/lib/presentation/widgets/requests/friendship_established_dialog.dart`
- **Reason**: User will see dialog when another user accepts their friend request (after FriendsChoiceDialog completion)
- **User Experience**: Dialog shows new friend's profile with "OK" button, celebrating the new friendship

## Included Notification Types ✅

These notification types **ARE** included because they don't have dedicated dialogs and should appear in the notifications list:

### **Response Notifications**
- `contactRequestAccepted` - User needs to know their request was accepted
- `contactRequestDeclined` - User needs to know their request was declined
- `bubbleJoinRequestRejected` - Shown to requester AND all bubble members when join request is denied
- `bubbleInviteRequestRejected` - Shown to invitee AND all bubble members when invitation is declined

### **Outcome Notifications**
- `bubbleVotekickPassed` - User needs to know the votekick succeeded

### **Communication Notifications**
- All `friend*MessageReceived` types - Direct messages from friends
- All `bubble*MessageReceived` types - Messages in bubble chats
- All call-related notifications - Incoming calls, missed calls, etc.

### **Lifecycle Notifications**
- All `bubblePopReminder*` types - Reminders about bubble expiration
- `bubblePopped` - Notification that bubble has ended
- All `inactiveNoBubble*` types - Engagement reminders

### **System Notifications**
- `statusUpdates` - General app status updates
- `securityAlerts` - Security-related notifications
- `appUpdates` - App update notifications

## Implementation Benefits

### **1. No Duplicate Notifications**
- Users don't see the same event twice
- Clean, focused notification list
- Better user experience

### **2. Clear Separation of Concerns**
- **Dialogs**: Immediate action required (accept/decline/vote)
- **Notifications**: Informational updates and responses

### **3. Database Efficiency**
- Fewer notification records to store
- Reduced database storage requirements
- Faster notification queries

### **4. Type Safety**
- Database prevents insertion of dialog-based notification types
- Compile-time safety for notification creation
- Clear documentation of what should/shouldn't be stored

## Testing Strategy

The test suite validates this exclusion strategy:

```go
// Test that dialog-based notification types are rejected
dialogBasedTypes := []string{
    "contactRequestReceived",    // Has ContactRequestDialog
    "bubbleInvitationReceived",  // Has BubbleInviteRequestDialog
    "bubbleJoinRequestReceived", // Has BubbleJoinRequestDialog
    "bubbleJoinRequestAccepted", // Shows BubbleMemberJoinedDialog
    "bubbleMemberJoined",        // Has BubbleMemberJoinedDialog
    "bubbleVotekickInitiated",   // Has BubbleKickoutRequestDialog
    "friendshipEstablished",     // Has FriendshipEstablishedDialog
}

for _, dialogType := range dialogBasedTypes {
    _, err = client.Pool.Exec(ctx, query, uuid.New(), testUserID2, dialogType, ...)
    assert.Error(t, err, "Should fail when inserting dialog-based notification type: %s", dialogType)
}
```

## Migration Considerations

### **Existing Code**
- Backend services creating these notification types will get database errors
- This is intentional and forces proper separation of dialog vs notification logic
- Services should trigger dialogs directly instead of creating notifications

### **Frontend Handling**
- MQTT messages can still trigger dialogs directly
- No need to create notification records for dialog-based events
- Cleaner real-time event handling

## New Dialogs Created

### **BubbleMemberJoinedDialog** ✅
- **Location**: `/Users/<USER>/Hopen/hopen/lib/presentation/widgets/requests/bubble_member_joined_dialog.dart`
- **Purpose**: Shows when someone joins the user's bubble
- **Features**:
  - Displays new member's profile picture and name
  - Shows the bubble name and join timestamp
  - Single "OK" button for acknowledgment
  - Same design pattern as other request dialogs
  - Animated gradient title and color transitions
- **Triggered by**: Both `bubbleJoinRequestAccepted` and `bubbleMemberJoined` events
- **User Experience**: Simple acknowledgment dialog, no actions required

### **FriendshipEstablishedDialog** ✅
- **Location**: `/Users/<USER>/Hopen/hopen/lib/presentation/widgets/requests/friendship_established_dialog.dart`
- **Purpose**: Shows when another user accepts the viewing user's friend request
- **Context**: Triggered when the viewing user completed FriendsChoiceDialog first, and the other user has now also completed their choice
- **Features**:
  - Displays new friend's profile picture and name
  - Shows "You and [Name] are now friends!" message
  - Shows friendship establishment timestamp
  - Single "OK" button for acknowledgment
  - Same design pattern as other request dialogs
  - Animated gradient title: "New friendship!"
- **Triggered by**: `friendshipEstablished` events
- **User Experience**: Celebratory dialog acknowledging the new friendship

## Multi-User Rejection Notifications

### **Important Clarification** 📢
The rejection notifications serve multiple users:

- **`bubbleJoinRequestRejected`**:
  - Shown to the **requester** (person who wanted to join)
  - Shown to **all bubble members** (to inform them of the decision)

- **`bubbleInviteRequestRejected`**:
  - Shown to the **invitee** (person who was invited)
  - Shown to **all bubble members** (to inform them of the decision)

This multi-user approach ensures transparency and keeps all relevant parties informed about bubble membership decisions.

## Conclusion

This exclusion strategy ensures:
- **No duplicate notifications** for events that show dialogs
- **Clear separation** between immediate actions (dialogs) and informational updates (notifications)
- **Better user experience** with focused, relevant notification lists
- **Multi-user transparency** for rejection notifications
- **Database efficiency** with fewer unnecessary records
- **Type safety** preventing incorrect notification creation

The schema now perfectly supports the app's dialog-based interaction model while maintaining a clean, efficient notification system.
