package user

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"

	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
)

// Service handles user operations
type Service struct {
	logger      *zap.Logger
	db          *database.PostgreSQLClient
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
}

// Dependencies holds the dependencies for the user service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
}

// New creates a new user service instance
func New(deps *Dependencies) *Service {
	return &Service{
		logger:      deps.Logger,
		db:          deps.DB,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
	}
}

// RegisterRoutes registers the user service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	router.GET("/search", s.searchUsers)

	// Availability checks (email / username)
	router.POST("/check-email", s.checkEmailAvailability)
	router.POST("/check-username", s.checkUsernameAvailability)

	router.GET("/:id", s.getUserByID)
	router.PUT("/:id", s.authMiddleware(), s.updateUser)
	router.DELETE("/:id", s.authMiddleware(), s.deleteUser)
	router.POST("/:id/ban", s.authMiddleware(), s.banUser)
	router.POST("/:id/unban", s.authMiddleware(), s.unbanUser)

	// Relationship management routes
	router.GET("/:id/relationships", s.authMiddleware(), s.getUserRelationships)
	router.GET("/:id/ban-details", s.authMiddleware(), s.getBanDetails)
	router.POST("/relationships", s.authMiddleware(), s.createUserRelationship)
	router.DELETE("/relationships/:fromUserId/:toUserId/:type", s.authMiddleware(), s.removeUserRelationship)
}

// User represents a user in the system
type User struct {
	ID                   string                 `json:"id"`
	Username             *string                `json:"username"`
	Email                string                 `json:"email"`
	FirstName            *string                `json:"first_name"`
	LastName             *string                `json:"last_name"`
	AvatarURL            *string                `json:"avatar_url"`
	DateOfBirth          *time.Time             `json:"date_of_birth"`
	IsActive             bool                   `json:"is_active"`
	IsPrivate            bool                   `json:"is_private"`
	NotificationSettings map[string]interface{} `json:"notification_settings"`

	// Pending request tracking fields
	PendingSentBubbleRequestUserIds     []string `json:"pending_sent_bubble_request_user_ids"`
	PendingReceivedBubbleRequestUserIds []string `json:"pending_received_bubble_request_user_ids"`
	PendingSentContactRequestIds        []string `json:"pending_sent_contact_request_ids"`
	PendingReceivedContactRequestIds    []string `json:"pending_received_contact_request_ids"`

	CreatedAt            time.Time              `json:"created_at"`
	UpdatedAt            time.Time              `json:"updated_at"`
}

// CreateUserRequest represents a user creation request
type CreateUserRequest struct {
	Username    *string                `json:"username"`
	Email       string                 `json:"email" binding:"required,email"`
	FirstName   *string                `json:"first_name"`
	LastName    *string                `json:"last_name"`
	AvatarURL   *string                `json:"avatar_url"`
	DateOfBirth *time.Time             `json:"date_of_birth"`
	IsPrivate   bool                   `json:"is_private"`
	NotificationSettings map[string]interface{} `json:"notification_settings"`
}

// UpdateUserRequest represents a user update request
type UpdateUserRequest struct {
	Username    *string                `json:"username"`
	FirstName   *string                `json:"first_name"`
	LastName    *string                `json:"last_name"`
	AvatarURL   *string                `json:"avatar_url"`
	DateOfBirth *time.Time             `json:"date_of_birth"`
	IsPrivate   *bool                  `json:"is_private"`
	NotificationSettings map[string]interface{} `json:"notification_settings"`
}

// SearchUsersResponse represents a user search response
type SearchUsersResponse struct {
	Users      []*User `json:"users"`
	TotalCount int     `json:"total_count"`
	Page       int     `json:"page"`
	PageSize   int     `json:"page_size"`
}

// Request structs for availability checks
type checkEmailRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type checkUsernameRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
}

// searchUsers handles user search with privacy filtering
func (s *Service) searchUsers(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// Rate limiting for search
	userID, exists := c.Get("user_id")
	if exists {
		allowed, err := s.rateLimiter.AllowSocialOperation(c.Request.Context(), userID.(string), "user_search")
		if err != nil {
			s.logger.Error("Rate limit check failed", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		if !allowed {
			c.JSON(http.StatusTooManyRequests, gin.H{"error": "Search rate limit exceeded"})
			return
		}
	}

	// Search users with privacy filtering
	users, err := s.db.SearchUsers(c.Request.Context(), query, pageSize)
	if err != nil {
		s.logger.Error("Failed to search users", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	// Convert database users to API users
	apiUsers := make([]*User, len(users))
	for i, dbUser := range users {
		apiUsers[i] = s.convertDBUserToAPIUser(dbUser)
	}

	response := &SearchUsersResponse{
		Users:      apiUsers,
		TotalCount: len(apiUsers),
		Page:       page,
		PageSize:   pageSize,
	}

	c.JSON(http.StatusOK, response)
}

// getUserByID handles getting a user by ID
func (s *Service) getUserByID(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	// Validate UUID format
	if _, err := uuid.Parse(userID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID format"})
		return
	}

	// Get user from database
	dbUser, err := s.db.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		s.logger.Error("Failed to get user by ID", 
			zap.String("user_id", userID), 
			zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Convert to API user
	apiUser := s.convertDBUserToAPIUser(dbUser)

	c.JSON(http.StatusOK, apiUser)
}

// updateUser handles updating a user
func (s *Service) updateUser(c *gin.Context) {
	userID := c.Param("id")
	currentUserID, _ := c.Get("user_id")

	// Check if user is updating their own profile
	if userID != currentUserID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only update your own profile"})
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get current user
	dbUser, err := s.db.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		s.logger.Error("Failed to get user for update", 
			zap.String("user_id", userID), 
			zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Update fields if provided
	if req.Username != nil {
		dbUser.Username = req.Username
	}
	if req.FirstName != nil {
		dbUser.FirstName = req.FirstName
	}
	if req.LastName != nil {
		dbUser.LastName = req.LastName
	}
	if req.AvatarURL != nil {
		dbUser.AvatarURL = req.AvatarURL
		s.logger.Info("Updating user avatar URL",
			zap.String("user_id", userID),
			zap.String("avatar_url", *req.AvatarURL))
	}
	if req.DateOfBirth != nil {
		dbUser.DateOfBirth = req.DateOfBirth
	}
	if req.IsPrivate != nil {
		dbUser.IsPrivate = *req.IsPrivate
	}
	if req.NotificationSettings != nil {
		dbUser.NotificationSettings = req.NotificationSettings
	}

	// Update timestamp
	dbUser.UpdatedAt = time.Now()

	// Save to database
	if err := s.db.UpdateUser(c.Request.Context(), dbUser); err != nil {
		s.logger.Error("Failed to update user in database",
			zap.String("user_id", userID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	// Update Ory identity traits if name/username fields were updated
	if req.FirstName != nil || req.LastName != nil || req.Username != nil {
		// Build traits map with proper nil checks
		traits := map[string]interface{}{
			"email": dbUser.Email,
		}

		// Add username with nil check
		if dbUser.Username != nil {
			traits["username"] = *dbUser.Username
		} else {
			traits["username"] = ""
		}

		// Add first_name with nil check
		if dbUser.FirstName != nil {
			traits["first_name"] = *dbUser.FirstName
		} else {
			traits["first_name"] = ""
		}

		// Add last_name with nil check
		if dbUser.LastName != nil {
			traits["last_name"] = *dbUser.LastName
		} else {
			traits["last_name"] = ""
		}

		s.logger.Info("Updating Ory identity traits",
			zap.String("user_id", userID),
			zap.Any("traits", traits))

		_, err := s.oryClient.UpdateIdentity(c.Request.Context(), userID, traits)
		if err != nil {
			s.logger.Error("Failed to update Ory identity traits",
				zap.String("user_id", userID),
				zap.Error(err))
			// Don't fail the request if Ory update fails, but log it
			// The database update was successful, which is the primary concern
		} else {
			s.logger.Info("Ory identity traits updated successfully",
				zap.String("user_id", userID))
		}
	}

	s.logger.Info("User updated successfully",
		zap.String("user_id", userID))

	// Convert to API user
	apiUser := s.convertDBUserToAPIUser(dbUser)

	c.JSON(http.StatusOK, apiUser)
}

// deleteUser handles user deletion (admin only)
func (s *Service) deleteUser(c *gin.Context) {
	userID := c.Param("id")
	currentUserID, _ := c.Get("user_id")

	// Check if user can delete this account
	canDelete, err := s.canDeleteUser(c.Request.Context(), currentUserID.(string), userID)
	if err != nil {
		s.logger.Error("Failed to check delete permissions", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !canDelete {
		c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient privileges"})
		return
	}

	// Soft delete user by setting is_active to false
	if err := s.db.SoftDeleteUser(c.Request.Context(), userID); err != nil {
		s.logger.Error("Failed to delete user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	s.logger.Info("User deleted successfully",
		zap.String("user_id", userID),
		zap.String("deleted_by", currentUserID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

// BanUserRequest represents the request payload for banning a user
type BanUserRequest struct {
	Reason       string                 `json:"reason,omitempty"`
	DurationDays *int                   `json:"duration_days,omitempty"` // nil for permanent ban
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// banUser handles user banning (admin only)
func (s *Service) banUser(c *gin.Context) {
	userID := c.Param("id")
	currentUserID, _ := c.Get("user_id")

	// Parse request body for ban details
	var req BanUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// If no body provided, use defaults
		req = BanUserRequest{
			Reason: "Administrative action",
		}
	}

	// Check admin privileges
	isAdmin, err := s.isUserAdmin(c.Request.Context(), currentUserID.(string))
	if err != nil {
		s.logger.Error("Failed to check admin privileges", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin privileges required"})
		return
	}

	// Check if user is already banned
	isBanned, existingBan, err := s.db.IsUserBanned(c.Request.Context(), userID)
	if err != nil {
		s.logger.Error("Failed to check ban status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if isBanned {
		c.JSON(http.StatusConflict, gin.H{
			"error": "User is already banned",
			"ban_details": gin.H{
				"banned_at": existingBan.CreatedAt,
				"reason":    existingBan.Reason,
				"banned_by": existingBan.CreatedBy,
			},
		})
		return
	}

	// Create ban relationship
	banRelationship := &database.UserRelationship{
		FromUserID:       userID,
		ToUserID:         userID, // For bans, both IDs are the same (user is banned)
		RelationshipType: "ban",
		Status:           "active",
		CreatedBy:        &currentUserID.(string),
		Reason:           &req.Reason,
		Metadata:         req.Metadata,
	}

	// Set expiration if duration is specified
	if req.DurationDays != nil && *req.DurationDays > 0 {
		expiresAt := time.Now().AddDate(0, 0, *req.DurationDays)
		banRelationship.ExpiresAt = &expiresAt
	}

	// Create the ban relationship
	if err := s.db.CreateUserRelationship(c.Request.Context(), banRelationship); err != nil {
		s.logger.Error("Failed to ban user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to ban user"})
		return
	}

	s.logger.Info("User banned",
		zap.String("user_id", userID),
		zap.String("banned_by", currentUserID.(string)),
		zap.String("reason", req.Reason),
		zap.Any("duration_days", req.DurationDays))

	response := gin.H{
		"message": "User banned successfully",
		"ban_details": gin.H{
			"user_id":    userID,
			"banned_by":  currentUserID.(string),
			"banned_at":  banRelationship.CreatedAt,
			"reason":     req.Reason,
			"expires_at": banRelationship.ExpiresAt,
		},
	}

	c.JSON(http.StatusOK, response)
}

// unbanUser handles user unbanning (admin only)
func (s *Service) unbanUser(c *gin.Context) {
	userID := c.Param("id")
	currentUserID, _ := c.Get("user_id")

	// Check admin privileges
	isAdmin, err := s.isUserAdmin(c.Request.Context(), currentUserID.(string))
	if err != nil {
		s.logger.Error("Failed to check admin privileges", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin privileges required"})
		return
	}

	// Check if user is actually banned
	isBanned, banDetails, err := s.db.IsUserBanned(c.Request.Context(), userID)
	if err != nil {
		s.logger.Error("Failed to check ban status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !isBanned {
		c.JSON(http.StatusNotFound, gin.H{"error": "User is not currently banned"})
		return
	}

	// Remove ban relationship
	if err := s.db.RemoveUserRelationship(c.Request.Context(), userID, userID, "ban"); err != nil {
		s.logger.Error("Failed to unban user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unban user"})
		return
	}

	s.logger.Info("User unbanned",
		zap.String("user_id", userID),
		zap.String("unbanned_by", currentUserID.(string)),
		zap.String("original_ban_reason", *banDetails.Reason),
		zap.String("original_banned_by", *banDetails.CreatedBy))

	response := gin.H{
		"message": "User unbanned successfully",
		"unban_details": gin.H{
			"user_id":     userID,
			"unbanned_by": currentUserID.(string),
			"unbanned_at": time.Now(),
			"original_ban": gin.H{
				"banned_at": banDetails.CreatedAt,
				"reason":    banDetails.Reason,
				"banned_by": banDetails.CreatedBy,
			},
		},
	}

	c.JSON(http.StatusOK, response)
}

// CreateUser creates a new user (used by auth service)
func (s *Service) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
	// Generate user ID
	userID := uuid.New().String()

	// Create database user
	dbUser := &database.User{
		ID:                   userID,
		Username:             req.Username,
		Email:                req.Email,
		FirstName:            req.FirstName,
		LastName:             req.LastName,
		AvatarURL:            req.AvatarURL,
		DateOfBirth:          req.DateOfBirth,
		IsActive:             true,
		IsPrivate:            req.IsPrivate,
		NotificationSettings: req.NotificationSettings,
	}

	// Set default notification settings if not provided
	if dbUser.NotificationSettings == nil {
		dbUser.NotificationSettings = map[string]interface{}{
			"birthday": map[string]interface{}{
				"email":   true,
				"enabled": true,
			},
			"bubble": map[string]interface{}{
				"email":   true,
				"enabled": true,
			},
			"friendship": map[string]interface{}{
				"email":   true,
				"enabled": true,
			},
			"contact": map[string]interface{}{
				"email":   true,
				"enabled": true,
			},
		}
	}

	// Save to database
	if err := s.db.CreateUser(ctx, dbUser); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Convert to API user
	apiUser := s.convertDBUserToAPIUser(dbUser)

	s.logger.Info("User created successfully", 
		zap.String("user_id", userID),
		zap.String("email", req.Email))

	return apiUser, nil
}

// convertDBUserToAPIUser converts a database user to an API user
func (s *Service) convertDBUserToAPIUser(dbUser *database.User) *User {
	return &User{
		ID:                   dbUser.ID,
		Username:             dbUser.Username,
		Email:                dbUser.Email,
		FirstName:            dbUser.FirstName,
		LastName:             dbUser.LastName,
		AvatarURL:            dbUser.AvatarURL,
		DateOfBirth:          dbUser.DateOfBirth,
		IsActive:             dbUser.IsActive,
		IsPrivate:            dbUser.IsPrivate,
		NotificationSettings: dbUser.NotificationSettings,

		// Pending request tracking fields
		PendingSentBubbleRequestUserIds:     dbUser.PendingSentBubbleRequestUserIds,
		PendingReceivedBubbleRequestUserIds: dbUser.PendingReceivedBubbleRequestUserIds,
		PendingSentContactRequestIds:        dbUser.PendingSentContactRequestIds,
		PendingReceivedContactRequestIds:    dbUser.PendingReceivedContactRequestIds,

		CreatedAt:            dbUser.CreatedAt,
		UpdatedAt:            dbUser.UpdatedAt,
	}
}




// authMiddleware provides authentication middleware (simplified)
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.AuthMiddleware(s.oryClient, s.logger)
}

// canDeleteUser checks if a user can delete another user account
func (s *Service) canDeleteUser(ctx context.Context, currentUserID, targetUserID string) (bool, error) {
	// Users can always delete their own account
	if currentUserID == targetUserID {
		return true, nil
	}

	// Check if current user is admin
	return s.isUserAdmin(ctx, currentUserID)
}

// isUserAdmin checks if a user has admin privileges
func (s *Service) isUserAdmin(ctx context.Context, userID string) (bool, error) {
	// Query user role from database
	query := `
		SELECT EXISTS(
			SELECT 1 FROM user_roles ur
			JOIN roles r ON ur.role_id = r.id
			WHERE ur.user_id = $1 AND r.name = 'admin' AND ur.is_active = true
		)`

	var isAdmin bool
	err := s.db.Pool.QueryRow(ctx, query, userID).Scan(&isAdmin)
	if err != nil {
		return false, fmt.Errorf("failed to check admin status: %w", err)
	}

	return isAdmin, nil
}

// CreateUserRelationshipRequest represents a request to create a user relationship
type CreateUserRelationshipRequest struct {
	FromUserID       string                 `json:"from_user_id" binding:"required"`
	ToUserID         string                 `json:"to_user_id" binding:"required"`
	RelationshipType string                 `json:"relationship_type" binding:"required"`
	Reason           *string                `json:"reason,omitempty"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
	ExpiresAt        *time.Time             `json:"expires_at,omitempty"`
}

// getUserRelationships handles getting all relationships for a user
func (s *Service) getUserRelationships(c *gin.Context) {
	userID := c.Param("id")
	currentUserID, _ := c.Get("user_id")

	// Check if user can view relationships (admin or self)
	canView := userID == currentUserID.(string)
	if !canView {
		isAdmin, err := s.isUserAdmin(c.Request.Context(), currentUserID.(string))
		if err != nil {
			s.logger.Error("Failed to check admin privileges", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		canView = isAdmin
	}

	if !canView {
		c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient privileges"})
		return
	}

	// Get relationship type filter from query params
	relationshipType := c.Query("type")
	var typeFilter *string
	if relationshipType != "" {
		typeFilter = &relationshipType
	}

	relationships, err := s.db.GetUserRelationships(c.Request.Context(), userID, typeFilter)
	if err != nil {
		s.logger.Error("Failed to get user relationships", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get relationships"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user_id": userID,
		"relationships": relationships,
		"count": len(relationships),
	})
}

// getBanDetails handles getting detailed ban information for a user
func (s *Service) getBanDetails(c *gin.Context) {
	userID := c.Param("id")
	currentUserID, _ := c.Get("user_id")

	// Check if user can view ban details (admin or self)
	canView := userID == currentUserID.(string)
	if !canView {
		isAdmin, err := s.isUserAdmin(c.Request.Context(), currentUserID.(string))
		if err != nil {
			s.logger.Error("Failed to check admin privileges", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		canView = isAdmin
	}

	if !canView {
		c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient privileges"})
		return
	}

	banDetails, err := s.db.GetBanDetails(c.Request.Context(), userID)
	if err != nil {
		s.logger.Error("Failed to get ban details", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get ban details"})
		return
	}

	if banDetails == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User is not banned"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user_id": userID,
		"ban_details": banDetails,
	})
}

// createUserRelationship handles creating a new user relationship (admin only for most types)
func (s *Service) createUserRelationship(c *gin.Context) {
	currentUserID, _ := c.Get("user_id")

	var req CreateUserRelationshipRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check permissions based on relationship type
	if req.RelationshipType == "ban" || req.RelationshipType == "restrict" {
		// Admin-only actions
		isAdmin, err := s.isUserAdmin(c.Request.Context(), currentUserID.(string))
		if err != nil {
			s.logger.Error("Failed to check admin privileges", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		if !isAdmin {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin privileges required"})
			return
		}
	} else if req.RelationshipType == "block" || req.RelationshipType == "mute" {
		// User can only create these relationships for themselves
		if req.FromUserID != currentUserID.(string) {
			c.JSON(http.StatusForbidden, gin.H{"error": "Can only create this relationship for yourself"})
			return
		}
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid relationship type"})
		return
	}

	// Create the relationship
	relationship := &database.UserRelationship{
		FromUserID:       req.FromUserID,
		ToUserID:         req.ToUserID,
		RelationshipType: req.RelationshipType,
		Status:           "active",
		CreatedBy:        &currentUserID.(string),
		Reason:           req.Reason,
		Metadata:         req.Metadata,
		ExpiresAt:        req.ExpiresAt,
	}

	if err := s.db.CreateUserRelationship(c.Request.Context(), relationship); err != nil {
		s.logger.Error("Failed to create user relationship", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create relationship"})
		return
	}

	s.logger.Info("User relationship created",
		zap.String("from_user_id", req.FromUserID),
		zap.String("to_user_id", req.ToUserID),
		zap.String("relationship_type", req.RelationshipType),
		zap.String("created_by", currentUserID.(string)))

	c.JSON(http.StatusCreated, gin.H{
		"message": "Relationship created successfully",
		"relationship": relationship,
	})
}

// removeUserRelationship handles removing a user relationship
func (s *Service) removeUserRelationship(c *gin.Context) {
	fromUserID := c.Param("fromUserId")
	toUserID := c.Param("toUserId")
	relationshipType := c.Param("type")
	currentUserID, _ := c.Get("user_id")

	// Check permissions based on relationship type
	if relationshipType == "ban" || relationshipType == "restrict" {
		// Admin-only actions
		isAdmin, err := s.isUserAdmin(c.Request.Context(), currentUserID.(string))
		if err != nil {
			s.logger.Error("Failed to check admin privileges", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		if !isAdmin {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin privileges required"})
			return
		}
	} else if relationshipType == "block" || relationshipType == "mute" {
		// User can only remove their own relationships
		if fromUserID != currentUserID.(string) {
			c.JSON(http.StatusForbidden, gin.H{"error": "Can only remove your own relationships"})
			return
		}
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid relationship type"})
		return
	}

	if err := s.db.RemoveUserRelationship(c.Request.Context(), fromUserID, toUserID, relationshipType); err != nil {
		s.logger.Error("Failed to remove user relationship", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove relationship"})
		return
	}

	s.logger.Info("User relationship removed",
		zap.String("from_user_id", fromUserID),
		zap.String("to_user_id", toUserID),
		zap.String("relationship_type", relationshipType),
		zap.String("removed_by", currentUserID.(string)))

	c.JSON(http.StatusOK, gin.H{
		"message": "Relationship removed successfully",
	})
}

// checkEmailAvailability returns whether an email is free to register
func (s *Service) checkEmailAvailability(c *gin.Context) {
	var req checkEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"available": false, "message": err.Error()})
		return
	}

	ctx := c.Request.Context()
	query := `SELECT EXISTS(SELECT 1 FROM users WHERE LOWER(email) = LOWER($1))`
	var exists bool
	if err := s.db.Pool.QueryRow(ctx, query, req.Email).Scan(&exists); err != nil {
		s.logger.Error("Failed to check email availability", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"available": false, "message": "Internal server error"})
		return
	}

	if exists {
		c.JSON(http.StatusOK, gin.H{"available": false, "message": "This email address is already registered"})
	} else {
		c.JSON(http.StatusOK, gin.H{"available": true, "message": "Email is available for registration"})
	}
}

// checkUsernameAvailability returns whether a username is free
func (s *Service) checkUsernameAvailability(c *gin.Context) {
	var req checkUsernameRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"available": false, "message": err.Error()})
		return
	}

	ctx := c.Request.Context()
	query := `SELECT EXISTS(SELECT 1 FROM users WHERE LOWER(username) = LOWER($1))`
	var exists bool
	if err := s.db.Pool.QueryRow(ctx, query, req.Username).Scan(&exists); err != nil {
		s.logger.Error("Failed to check username availability", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"available": false, "message": "Internal server error"})
		return
	}

	if exists {
		c.JSON(http.StatusOK, gin.H{"available": false, "message": "This username is already taken"})
	} else {
		c.JSON(http.StatusOK, gin.H{"available": true, "message": "Username is available"})
	}
}
