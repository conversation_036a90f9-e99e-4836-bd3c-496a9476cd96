package friendship

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"

	"hopenbackend/pkg/database"
)

// PostgreSQLRepository implements friendship operations using PostgreSQL
type PostgreSQLRepository struct {
	db     *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLRepository creates a new PostgreSQL repository for friendships
func NewPostgreSQLRepository(db *pgxpool.Pool, logger *zap.Logger) *PostgreSQLRepository {
	return &PostgreSQLRepository{
		db:     db,
		logger: logger,
	}
}

// Friendship represents a friendship relationship in PostgreSQL
type Friendship struct {
	ID             string    `json:"id"`
	User1ID        string    `json:"user1_id"`
	User2ID        string    `json:"user2_id"`
	CreatedAt      time.Time `json:"created_at"`
	SourceBubbleID *string   `json:"source_bubble_id,omitempty"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// FriendRequest represents a friend request in PostgreSQL
type FriendRequest struct {
	ID             string    `json:"id"`
	RequesterID    string    `json:"requester_id"`
	RecipientID    string    `json:"recipient_id"`
	Status         string    `json:"status"` // pending, accepted, declined
	CreatedAt      time.Time `json:"created_at"`
	SourceBubbleID *string   `json:"source_bubble_id,omitempty"`
	AutoGenerated  bool      `json:"auto_generated"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// CreateFriendship creates a new friendship
func (r *PostgreSQLRepository) CreateFriendship(ctx context.Context, friendship *Friendship) error {
	friendship.ID = uuid.New().String()
	friendship.CreatedAt = time.Now()
	friendship.UpdatedAt = time.Now()

	query := `
		INSERT INTO friendships (id, user1_id, user2_id, created_at, source_bubble_id, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)`

	_, err := r.db.Exec(ctx, query,
		friendship.ID,
		friendship.User1ID,
		friendship.User2ID,
		friendship.CreatedAt,
		friendship.SourceBubbleID,
		friendship.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("Failed to create friendship", zap.Error(err))
		return database.HandlePgxError(err, "friendships")
	}

	return nil
}

// GetFriendshipsByUser retrieves friendships for a user
func (r *PostgreSQLRepository) GetFriendshipsByUser(ctx context.Context, userID string) ([]*Friendship, error) {
	query := `
		SELECT id, user1_id, user2_id, created_at, source_bubble_id, updated_at
		FROM friendships
		WHERE user1_id = $1 OR user2_id = $1
		ORDER BY created_at DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query friendships: %w", err)
	}
	defer rows.Close()

	var friendships []*Friendship
	for rows.Next() {
		var friendship Friendship
		var sourceBubbleID sql.NullString

		err := rows.Scan(
			&friendship.ID,
			&friendship.User1ID,
			&friendship.User2ID,
			&friendship.CreatedAt,
			&sourceBubbleID,
			&friendship.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan friendship row", zap.Error(err))
			continue
		}

		if sourceBubbleID.Valid {
			friendship.SourceBubbleID = &sourceBubbleID.String
		}

		friendships = append(friendships, &friendship)
	}

	return friendships, nil
}

// FriendshipExists checks if a friendship exists between two users
func (r *PostgreSQLRepository) FriendshipExists(ctx context.Context, userID1, userID2 string) (bool, error) {
	query := `
		SELECT EXISTS(
			SELECT 1 FROM friendships
			WHERE (user1_id = $1 AND user2_id = $2) OR 
			      (user1_id = $2 AND user2_id = $1)
		)`

	var exists bool
	err := r.db.QueryRow(ctx, query, userID1, userID2).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check friendship existence: %w", err)
	}

	return exists, nil
}

// GetMutualFriends retrieves mutual friends between two users
func (r *PostgreSQLRepository) GetMutualFriends(ctx context.Context, userID1, userID2 string) ([]string, error) {
	query := `
		WITH user1_friends AS (
			SELECT CASE 
				WHEN user1_id = $1 THEN user2_id 
				ELSE user1_id 
			END AS friend_id
			FROM friendships
			WHERE user1_id = $1 OR user2_id = $1
		),
		user2_friends AS (
			SELECT CASE 
				WHEN user1_id = $2 THEN user2_id 
				ELSE user1_id 
			END AS friend_id
			FROM friendships
			WHERE user1_id = $2 OR user2_id = $2
		)
		SELECT u1.friend_id
		FROM user1_friends u1
		INNER JOIN user2_friends u2 ON u1.friend_id = u2.friend_id`

	rows, err := r.db.Query(ctx, query, userID1, userID2)
	if err != nil {
		return nil, fmt.Errorf("failed to query mutual friends: %w", err)
	}
	defer rows.Close()

	var friendIDs []string
	for rows.Next() {
		var friendID string
		if err := rows.Scan(&friendID); err != nil {
			r.logger.Error("Failed to scan mutual friend row", zap.Error(err))
			continue
		}
		friendIDs = append(friendIDs, friendID)
	}

	return friendIDs, nil
}

// CreateFriendRequest creates a new friend request
func (r *PostgreSQLRepository) CreateFriendRequest(ctx context.Context, request *FriendRequest) error {
	request.ID = uuid.New().String()
	request.CreatedAt = time.Now()
	request.UpdatedAt = time.Now()

	query := `
		INSERT INTO friend_requests (id, requester_id, recipient_id, status, created_at, source_bubble_id, auto_generated, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`

	_, err := r.db.Exec(ctx, query,
		request.ID,
		request.RequesterID,
		request.RecipientID,
		request.Status,
		request.CreatedAt,
		request.SourceBubbleID,
		request.AutoGenerated,
		request.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("Failed to create friend request", zap.Error(err))
		return fmt.Errorf("failed to create friend request: %w", err)
	}

	return nil
}

// GetFriendRequestByID retrieves a friend request by ID
func (r *PostgreSQLRepository) GetFriendRequestByID(ctx context.Context, requestID string) (*FriendRequest, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, created_at, source_bubble_id, auto_generated, updated_at
		FROM friend_requests
		WHERE id = $1`

	var request FriendRequest
	var sourceBubbleID sql.NullString

	err := r.db.QueryRow(ctx, query, requestID).Scan(
		&request.ID,
		&request.RequesterID,
		&request.RecipientID,
		&request.Status,
		&request.CreatedAt,
		&sourceBubbleID,
		&request.AutoGenerated,
		&request.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("friend request not found")
		}
		return nil, fmt.Errorf("failed to get friend request: %w", err)
	}

	if sourceBubbleID.Valid {
		request.SourceBubbleID = &sourceBubbleID.String
	}

	return &request, nil
}

// UpdateFriendRequestStatus updates the status of a friend request
func (r *PostgreSQLRepository) UpdateFriendRequestStatus(ctx context.Context, requestID, status string) error {
	query := `
		UPDATE friend_requests 
		SET status = $1, updated_at = NOW()
		WHERE id = $2`

	result, err := r.db.Exec(ctx, query, status, requestID)
	if err != nil {
		return fmt.Errorf("failed to update friend request status: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("friend request not found")
	}

	return nil
}

// GetFriendRequestsByUser retrieves friend requests for a user
func (r *PostgreSQLRepository) GetFriendRequestsByUser(ctx context.Context, userID string) ([]*FriendRequest, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, created_at, source_bubble_id, auto_generated, updated_at
		FROM friend_requests
		WHERE recipient_id = $1 AND status = 'pending'
		ORDER BY created_at DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query friend requests: %w", err)
	}
	defer rows.Close()

	var requests []*FriendRequest
	for rows.Next() {
		var request FriendRequest
		var sourceBubbleID sql.NullString

		err := rows.Scan(
			&request.ID,
			&request.RequesterID,
			&request.RecipientID,
			&request.Status,
			&request.CreatedAt,
			&sourceBubbleID,
			&request.AutoGenerated,
			&request.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan friend request row", zap.Error(err))
			continue
		}

		if sourceBubbleID.Valid {
			request.SourceBubbleID = &sourceBubbleID.String
		}

		requests = append(requests, &request)
	}

	return requests, nil
}

// GetReciprocalFriendRequest finds a reciprocal friend request between two users
func (r *PostgreSQLRepository) GetReciprocalFriendRequest(ctx context.Context, userID1, userID2 string) (*FriendRequest, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, created_at, source_bubble_id, auto_generated, updated_at
		FROM friend_requests
		WHERE requester_id = $2 AND recipient_id = $1
		ORDER BY created_at DESC
		LIMIT 1`

	var request FriendRequest
	var sourceBubbleID sql.NullString

	err := r.db.QueryRow(ctx, query, userID1, userID2).Scan(
		&request.ID,
		&request.RequesterID,
		&request.RecipientID,
		&request.Status,
		&request.CreatedAt,
		&sourceBubbleID,
		&request.AutoGenerated,
		&request.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil // No reciprocal request found
		}
		return nil, fmt.Errorf("failed to get reciprocal friend request: %w", err)
	}

	if sourceBubbleID.Valid {
		request.SourceBubbleID = &sourceBubbleID.String
	}

	return &request, nil
}

// DeleteFriendRequest deletes a friend request
func (r *PostgreSQLRepository) DeleteFriendRequest(ctx context.Context, requestID string) error {
	query := `DELETE FROM friend_requests WHERE id = $1`

	result, err := r.db.Exec(ctx, query, requestID)
	if err != nil {
		return fmt.Errorf("failed to delete friend request: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("friend request not found")
	}

	return nil
}

// FriendRequestExists checks if a friend request already exists between two users for a specific bubble
func (r *PostgreSQLRepository) FriendRequestExists(ctx context.Context, requesterID, recipientID, sourceBubbleID string) (bool, error) {
	query := `
		SELECT EXISTS(
			SELECT 1 FROM friend_requests
			WHERE requester_id = $1 AND recipient_id = $2 AND source_bubble_id = $3
		)`

	var exists bool
	err := r.db.QueryRow(ctx, query, requesterID, recipientID, sourceBubbleID).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check friend request existence: %w", err)
	}

	return exists, nil
}

// GetFriendshipByID retrieves a friendship by ID
func (r *PostgreSQLRepository) GetFriendshipByID(ctx context.Context, friendshipID string) (*Friendship, error) {
	query := `
		SELECT id, user1_id, user2_id, created_at, source_bubble_id, updated_at
		FROM friendships
		WHERE id = $1`

	var friendship Friendship
	var sourceBubbleID sql.NullString

	err := r.db.QueryRow(ctx, query, friendshipID).Scan(
		&friendship.ID,
		&friendship.User1ID,
		&friendship.User2ID,
		&friendship.CreatedAt,
		&sourceBubbleID,
		&friendship.UpdatedAt,
	)

	if err != nil {
		return nil, database.HandlePgxError(err, "friendships")
	}

	if sourceBubbleID.Valid {
		friendship.SourceBubbleID = &sourceBubbleID.String
	}

	return &friendship, nil
}

// DeleteFriendship deletes a friendship
func (r *PostgreSQLRepository) DeleteFriendship(ctx context.Context, friendshipID string) error {
	query := `DELETE FROM friendships WHERE id = $1`

	result, err := r.db.Exec(ctx, query, friendshipID)
	if err != nil {
		return fmt.Errorf("failed to delete friendship: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("friendship not found")
	}

	return nil
}
