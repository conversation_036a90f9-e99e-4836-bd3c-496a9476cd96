package bubble

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"hopenbackend/pkg/database"
	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/ory"
)

// NotificationService interface for sending notifications
type NotificationService interface {
	SendBubbleInviteNotification(ctx context.Context, recipientID, senderID, bubbleID string) error
	SendBubbleJoinRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID string) error
	SendBubbleJoinRequestAcceptedNotification(ctx context.Context, requesterID, acceptedByUserID, bubbleID string) error
	SendBubbleJoinRequestRejectedNotification(ctx context.Context, requesterID, rejectedByUserID, bubbleID string) error
	SendBubbleMemberJoinedNotification(ctx context.Context, memberID, newMemberID, bubbleID string) error
	SendBubbleProposeRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID, proposedUserID string) error
	SendBubbleKickoutRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID, targetUserID string) error
	SendBubbleVotekickInitiatedNotification(ctx context.Context, memberID, initiatorID, targetUserID, bubbleID string) error
	SendBubbleVotekickPassedNotification(ctx context.Context, memberID, targetUserID, bubbleID string) error
	SendBubbleStartRequestNotification(ctx context.Context, recipientID, requesterID string) error
	SendBubbleExpiryNotification(ctx context.Context, userID, bubbleID string, daysLeft int) error
	SendBubblePoppedNotification(ctx context.Context, userID, bubbleID, bubbleName string, popTime time.Time) error
}

// Service represents the bubble microservice
type Service struct {
	db                  *database.PostgreSQLClient
	oryClient           *ory.Client
	natsConn            *nats.Conn
	js                  nats.JetStreamContext
	logger              *zap.Logger
	notificationService NotificationService
}

// BubbleMemberJoinedEvent represents the NATS event for member joining
type BubbleMemberJoinedEvent struct {
	EventType string `json:"event_type"`
	BubbleID  string `json:"bubble_id"`
	UserID    string `json:"user_id"`
	MemberID  string `json:"member_id"`
	Timestamp int64  `json:"timestamp"`
}

// BubbleMemberLeftEvent represents the NATS event for member leaving
type BubbleMemberLeftEvent struct {
	EventType string `json:"event_type"`
	BubbleID  string `json:"bubble_id"`
	UserID    string `json:"user_id"`
	Reason    string `json:"reason"`
	Timestamp int64  `json:"timestamp"`
}

// BubbleExpireEvent represents the scheduled expiry event
type BubbleExpireEvent struct {
	EventType string `json:"event_type"`
	BubbleID  string `json:"bubble_id"`
	Timestamp int64  `json:"timestamp"`
}

// BubbleExpiredEvent represents the event published when a bubble expires
type BubbleExpiredEvent struct {
	BubbleID  string   `json:"bubble_id"`
	Members   []string `json:"members"`
	ExpiredAt int64    `json:"expired_at"`
}

// BubbleExpiryReminderEvent represents a scheduled expiry reminder event
type BubbleExpiryReminderEvent struct {
	EventType string `json:"event_type"`
	BubbleID  string `json:"bubble_id"`
	DaysLeft  int    `json:"days_left"`
	Timestamp int64  `json:"timestamp"`
}

// NewService creates a new bubble service instance
func NewService(
	db *database.PostgreSQLClient,
	oryClient *ory.Client,
	natsConn *nats.Conn,
	logger *zap.Logger,
	notificationService NotificationService,
) *Service {
	service := &Service{
		db:                  db,
		oryClient:           oryClient,
		natsConn:            natsConn,
		logger:              logger,
		notificationService: notificationService,
	}

	// Initialize JetStream if NATS is available
	if natsConn != nil {
		if err := service.initializeJetStream(); err != nil {
			logger.Error("Failed to initialize JetStream", zap.Error(err))
		}
	}

	return service
}

// RegisterRoutes registers the bubble service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	router.Use(s.authMiddleware())
	{
		// Basic bubble operations
		router.GET("", s.getBubbles)
		router.POST("", s.createBubble)
		router.GET("/:id", s.getBubble)
		router.PUT("/:id", s.updateBubble)
		router.DELETE("/:id", s.deleteBubble)

		// Membership operations
		router.GET("/:id/members", s.getBubbleMembers)
		router.POST("/:id/join", s.joinBubble)
		router.DELETE("/:id/leave", s.leaveBubble)

		// Request operations
		router.GET("/:id/requests", s.getBubbleRequests)
		router.POST("/:id/requests/:requestId/accept", s.acceptBubbleRequest)
		router.POST("/:id/requests/:requestId/decline", s.declineBubbleRequest)
		router.POST("/:id/invite", s.inviteToBubble)
		router.POST("/:id/propose", s.proposeUserToBubble)
		router.POST("/:id/kickout", s.kickoutFromBubble)

		// Start request (for requesting to start a bubble with someone)
		router.POST("/start-request", s.createStartRequest)

		// Generic request endpoints (for any request type)
		router.GET("/requests/pending", s.getUserPendingRequests)
		router.POST("/requests/:requestId/accept", s.acceptAnyRequest)
		router.POST("/requests/:requestId/decline", s.declineAnyRequest)

		// Bubble badge status endpoints (optimized for performance)
		router.POST("/badge-status", s.getBubbleBadgeStatus)
		router.GET("/badge-status/:user_id", s.getSingleUserBubbleBadgeStatus)
	}
}

// Bubble represents a bubble entity
type Bubble struct {
	ID              string    `json:"id"`
	Name            string    `json:"name"`
	MaxMembers      int       `json:"capacity"`
	CurrentMembers  int       `json:"current_members"`
	LifecycleStatus string    `json:"status"`
	CreatedBy       string    `json:"creator_id"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	ExpiresAt       time.Time `json:"expires_at"`
}

// BubbleMember represents a bubble member
type BubbleMember struct {
	ID        string     `json:"id"`
	BubbleID  string     `json:"bubble_id"`
	UserID    string     `json:"user_id"`
	Status    string     `json:"status"`
	JoinedAt  *time.Time `json:"joined_at,omitempty"`
	LeftAt    *time.Time `json:"left_at,omitempty"`
	InvitedBy *string    `json:"invited_by,omitempty"`
}

// BubbleRequest represents a bubble request
type BubbleRequest struct {
	ID          string    `json:"id"`
	BubbleID    string    `json:"bubble_id"`
	RequesterID string    `json:"requester_id"`
	RecipientID string    `json:"recipient_id"`
	RequestType string    `json:"request_type"`
	Status      string    `json:"status"`
	Message     string    `json:"message"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	ExpiresAt   time.Time `json:"expires_at"`
	Username    string    `json:"username,omitempty"`
	FirstName   string    `json:"first_name,omitempty"`
	LastName    string    `json:"last_name,omitempty"`
}

// Request DTOs
type CreateBubbleRequest struct {
	Name       string `json:"name" binding:"required"`
	MaxMembers int    `json:"capacity" binding:"required,min=2,max=5"`
}

type UpdateBubbleRequest struct {
	Name *string `json:"name"`
}

type JoinBubbleRequest struct {
	Message string `json:"message"`
}

type InviteToBubbleRequest struct {
	RecipientID string `json:"recipient_id" binding:"required"`
	Message     string `json:"message"`
}

type ProposeUserRequest struct {
	ProposedUserID string `json:"proposed_user_id" binding:"required"`
	Message        string `json:"message"`
}

type StartBubbleWithContactRequest struct {
	ContactID  string `json:"contact_id" binding:"required"`
	BubbleName string `json:"bubble_name" binding:"required"`
	Capacity   int    `json:"capacity" binding:"required,min=2,max=5"`
}

// getBubbles handles getting bubbles for a user
func (s *Service) getBubbles(c *gin.Context) {
	userID, _ := c.Get("user_id")

	// Get user's bubble memberships
	rows, err := s.db.Pool.Query(c.Request.Context(), `
		SELECT b.id, b.name, b.capacity, b.current_members,
		       b.status, b.creator_id, b.created_at, b.updated_at, b.expires_at
		FROM bubbles b
		JOIN bubble_members bm ON b.id = bm.bubble_id
		WHERE bm.user_id = $1 AND bm.status = 'active'
		ORDER BY b.created_at DESC`,
		userID.(string))

	if err != nil {
		s.logger.Error("Failed to get bubbles", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get bubbles"})
		return
	}
	defer rows.Close()

	var bubbles []Bubble
	for rows.Next() {
		var bubble Bubble
		err := rows.Scan(
			&bubble.ID, &bubble.Name, &bubble.MaxMembers,
			&bubble.CurrentMembers, &bubble.LifecycleStatus, &bubble.CreatedBy,
			&bubble.CreatedAt, &bubble.UpdatedAt, &bubble.ExpiresAt)
		if err != nil {
			s.logger.Error("Failed to scan bubble", zap.Error(err))
			continue
		}
		bubbles = append(bubbles, bubble)
	}

	c.JSON(http.StatusOK, gin.H{"bubbles": bubbles})
}

// createBubble handles creating a new bubble
func (s *Service) createBubble(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req CreateBubbleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create bubble
	bubbleID := uuid.New().String()
	now := time.Now()
	expiresAt := now.AddDate(0, 0, 90) // 90 days from creation

	_, err := s.db.Pool.Exec(c.Request.Context(), `
		INSERT INTO bubbles (id, name, capacity, current_members,
		                   status, creator_id, created_at, updated_at, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
		bubbleID, req.Name, req.MaxMembers, 1,
		"active", userID.(string), now, now, expiresAt)

	if err != nil {
		s.logger.Error("Failed to create bubble", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create bubble"})
		return
	}

	// Add creator as first member
	memberID := uuid.New().String()
	_, err = s.db.Pool.Exec(c.Request.Context(), `
		INSERT INTO bubble_members (id, bubble_id, user_id, status, joined_at)
		VALUES ($1, $2, $3, $4, $5)`,
		memberID, bubbleID, userID.(string), "active", now)

	if err != nil {
		s.logger.Error("Failed to add creator to bubble", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create bubble"})
		return
	}

	// Publish member joined event for creator
	if err := s.publishMemberJoinedEvent(bubbleID, userID.(string), memberID); err != nil {
		s.logger.Error("Failed to publish member joined event for creator", zap.Error(err))
		// Don't fail the request, but log the error
	}

	// Schedule expiry event using NATS Cron
	if err := s.scheduleExpiryEvent(bubbleID, expiresAt); err != nil {
		s.logger.Error("Failed to schedule bubble expiry", zap.Error(err))
	}

	// Schedule expiry reminder notifications
	if err := s.scheduleExpiryReminders(bubbleID, expiresAt); err != nil {
		s.logger.Error("Failed to schedule bubble expiry reminders", zap.Error(err))
	}

	bubble := Bubble{
		ID:              bubbleID,
		Name:            req.Name,
		MaxMembers:      req.MaxMembers,
		CurrentMembers:  1,
		LifecycleStatus: "active",
		CreatedBy:       userID.(string),
		CreatedAt:       now,
		UpdatedAt:       now,
		ExpiresAt:       expiresAt,
	}

	c.JSON(http.StatusCreated, gin.H{"bubble": bubble})
}

// getBubble handles getting a specific bubble
func (s *Service) getBubble(c *gin.Context) {
	bubbleID := c.Param("id")

	bubble, err := s.getBubbleFromDB(c.Request.Context(), bubbleID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Bubble not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"bubble": bubble})
}

// updateBubble handles updating a bubble
func (s *Service) updateBubble(c *gin.Context) {
	userID, _ := c.Get("user_id")
	_ = userID // all members are equal; no creator privilege
	bubbleID := c.Param("id")

	var req UpdateBubbleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update bubble
	_, err := s.db.Pool.Exec(c.Request.Context(), `
		UPDATE bubbles SET name = COALESCE($1, name),
		                 updated_at = $2 WHERE id = $3`,
		req.Name, time.Now(), bubbleID)

	if err != nil {
		s.logger.Error("Failed to update bubble", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update bubble"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Bubble updated successfully"})
}

// deleteBubble handles deleting a bubble
func (s *Service) deleteBubble(c *gin.Context) {
	userID, _ := c.Get("user_id")
	_ = userID // all members have equal privileges; variable kept for future use
	bubbleID := c.Param("id")

	// Soft delete bubble
	_, err := s.db.Pool.Exec(c.Request.Context(), `
		UPDATE bubbles SET status = 'archived', updated_at = $1 WHERE id = $2`,
		time.Now(), bubbleID)

	if err != nil {
		s.logger.Error("Failed to delete bubble", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete bubble"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Bubble deleted successfully"})
}

// authMiddleware provides authentication middleware using the comprehensive auth middleware
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.AuthMiddleware(s.oryClient, s.logger)
}

// Helper methods

// getBubbleFromDB retrieves a bubble from the database
func (s *Service) getBubbleFromDB(ctx context.Context, bubbleID string) (*Bubble, error) {
	var bubble Bubble
	err := s.db.Pool.QueryRow(ctx, `
		SELECT id, name, capacity, current_members,
		       status, creator_id, created_at, updated_at, expires_at
		FROM bubbles WHERE id = $1`,
		bubbleID).Scan(
		&bubble.ID, &bubble.Name, &bubble.MaxMembers,
		&bubble.CurrentMembers, &bubble.LifecycleStatus, &bubble.CreatedBy,
		&bubble.CreatedAt, &bubble.UpdatedAt, &bubble.ExpiresAt)

	if err != nil {
		return nil, err
	}

	return &bubble, nil
}

// getBubbleMembers retrieves active member IDs for a bubble
func (s *Service) getBubbleMembers(c *gin.Context) {
	bubbleID := c.Param("id")

	rows, err := s.db.Pool.Query(c.Request.Context(), `
		SELECT bm.id, bm.user_id, bm.status, bm.joined_at,
		       u.username, u.first_name, u.last_name
		FROM bubble_members bm
		JOIN users u ON bm.user_id = u.id
		WHERE bm.bubble_id = $1 AND bm.status = 'active'
		ORDER BY bm.joined_at ASC`,
		bubbleID)

	if err != nil {
		s.logger.Error("Failed to get bubble members", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get members"})
		return
	}
	defer rows.Close()

	var members []map[string]interface{}
	for rows.Next() {
		var memberID, userID, status, username string
		var joinedAt time.Time
		var firstName, lastName *string

		err := rows.Scan(&memberID, &userID, &status, &joinedAt, &username, &firstName, &lastName)
		if err != nil {
			s.logger.Error("Failed to scan bubble member", zap.Error(err))
			continue
		}

		member := map[string]interface{}{
			"id":        memberID,
			"user_id":   userID,
			"status":    status,
			"joined_at": joinedAt,
			"username":  username,
		}

		if firstName != nil {
			member["first_name"] = *firstName
		}
		if lastName != nil {
			member["last_name"] = *lastName
		}

		members = append(members, member)
	}

	c.JSON(http.StatusOK, gin.H{"members": members})
}

// joinBubble handles joining a bubble (creates request)
func (s *Service) joinBubble(c *gin.Context) {
	userID, _ := c.Get("user_id")
	bubbleID := c.Param("id")

	var req JoinBubbleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if bubble exists and is active
	var bubble Bubble
	err := s.db.Pool.QueryRow(c.Request.Context(), `
		SELECT id, name, capacity, current_members, status, expires_at, created_at
		FROM bubbles WHERE id = $1 AND status = 'active'`, bubbleID).Scan(
		&bubble.ID, &bubble.Name, &bubble.MaxMembers, &bubble.CurrentMembers,
		&bubble.LifecycleStatus, &bubble.ExpiresAt, &bubble.CreatedAt)

	if err != nil {
		s.logger.Error("Bubble not found or not active", zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "Bubble not found or not active"})
		return
	}

	// Check if user is already a member
	var existingMember bool
	err = s.db.Pool.QueryRow(c.Request.Context(), `
		SELECT EXISTS(SELECT 1 FROM bubble_members WHERE bubble_id = $1 AND user_id = $2 AND status = 'active')`,
		bubbleID, userID.(string)).Scan(&existingMember)

	if err != nil {
		s.logger.Error("Failed to check existing membership", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check membership"})
		return
	}

	if existingMember {
		c.JSON(http.StatusConflict, gin.H{"error": "Already a member of this bubble"})
		return
	}

	// Check if bubble has capacity
	if bubble.CurrentMembers >= bubble.MaxMembers {
		c.JSON(http.StatusConflict, gin.H{"error": "Bubble is at full capacity"})
		return
	}

	// Create join request
	requestID := uuid.New().String()
	_, err = s.db.Pool.Exec(c.Request.Context(), `
		INSERT INTO bubble_requests (id, bubble_id, requester_id, request_type, status, message, created_at, updated_at, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
		requestID, bubbleID, userID.(string), "join", "pending", req.Message,
		time.Now(), time.Now(), time.Now().Add(7*24*time.Hour))

	if err != nil {
		s.logger.Error("Failed to create join request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create join request"})
		return
	}

	// Send notifications to bubble members
	go func() {
		memberRows, err := s.db.Pool.Query(context.Background(), `
			SELECT user_id FROM bubble_members
			WHERE bubble_id = $1 AND status = 'active'`, bubbleID)
		if err != nil {
			s.logger.Error("Failed to get bubble members for notification", zap.Error(err))
			return
		}
		defer memberRows.Close()

		for memberRows.Next() {
			var memberID string
			if err := memberRows.Scan(&memberID); err != nil {
				continue
			}

			// Don't notify the requester
			if memberID == userID.(string) {
				continue
			}

			if err := s.notificationService.SendBubbleJoinRequestNotification(context.Background(), memberID, userID.(string), bubbleID); err != nil {
				s.logger.Warn("Failed to send join request notification",
					zap.String("member_id", memberID),
					zap.String("requester_id", userID.(string)),
					zap.Error(err))
			}
		}
	}()

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Join request created successfully",
		"request_id": requestID,
		"bubble": gin.H{
			"id":              bubble.ID,
			"name":            bubble.Name,
			"current_members": bubble.CurrentMembers,
			"capacity":        bubble.MaxMembers,
			"expires_at":      bubble.ExpiresAt,
		},
	})
}

// leaveBubble handles leaving a bubble
func (s *Service) leaveBubble(c *gin.Context) {
	userID, _ := c.Get("user_id")
	bubbleID := c.Param("id")

	// Update membership status to removed
	_, err := s.db.Pool.Exec(c.Request.Context(), `
		UPDATE bubble_members SET status = 'removed', left_at = $1, updated_at = $2
		WHERE bubble_id = $3 AND user_id = $4 AND status = 'active'`,
		time.Now(), time.Now(), bubbleID, userID.(string))

	if err != nil {
		s.logger.Error("Failed to leave bubble", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to leave bubble"})
		return
	}

	// Publish member left event
	if err := s.publishMemberLeftEvent(bubbleID, userID.(string), "left"); err != nil {
		s.logger.Error("Failed to publish member left event", zap.Error(err))
		// Don't fail the request since the database operation was successful
	}

	c.JSON(http.StatusOK, gin.H{"message": "Left bubble successfully"})
}

// getBubbleRequests retrieves pending requests for a bubble
func (s *Service) getBubbleRequests(c *gin.Context) {
	bubbleID := c.Param("id")

	rows, err := s.db.Pool.Query(c.Request.Context(), `
		SELECT br.id, br.requester_id, br.request_type, br.status, br.message, br.created_at,
		       u.username, u.first_name, u.last_name
		FROM bubble_requests br
		JOIN users u ON br.requester_id = u.id
		WHERE br.bubble_id = $1 AND br.status = 'pending'
		ORDER BY br.created_at ASC`,
		bubbleID)

	if err != nil {
		s.logger.Error("Failed to get bubble requests", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get requests"})
		return
	}
	defer rows.Close()

	var requests []BubbleRequest
	for rows.Next() {
		var req BubbleRequest
		var firstName, lastName *string
		err := rows.Scan(&req.ID, &req.RequesterID, &req.RequestType, &req.Status,
			&req.Message, &req.CreatedAt, &req.Username, &firstName, &lastName)
		if err != nil {
			s.logger.Error("Failed to scan bubble request", zap.Error(err))
			continue
		}

		if firstName != nil {
			req.FirstName = *firstName
		}
		if lastName != nil {
			req.LastName = *lastName
		}

		requests = append(requests, req)
	}

	c.JSON(http.StatusOK, gin.H{"requests": requests})
}

// acceptBubbleRequest accepts a pending bubble join request
func (s *Service) acceptBubbleRequest(c *gin.Context) {
	requestID := c.Param("requestId")

	// Begin transaction
	tx, err := s.db.Pool.Begin(c.Request.Context())
	if err != nil {
		s.logger.Error("Failed to begin transaction", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	defer tx.Rollback(c.Request.Context())

	// Get request details
	var bubbleID, requesterID string
	err = tx.QueryRow(c.Request.Context(),
		"SELECT bubble_id, requester_id FROM bubble_requests WHERE id = $1 AND status = 'pending'",
		requestID).Scan(&bubbleID, &requesterID)

	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Request not found or not pending"})
		return
	}

	// Get bubble info for expiry extension logic
	var bubble Bubble
	err = tx.QueryRow(c.Request.Context(), `
		SELECT id, name, capacity, current_members, status, expires_at, created_at
		FROM bubbles WHERE id = $1 AND status = 'active'`, bubbleID).Scan(
		&bubble.ID, &bubble.Name, &bubble.MaxMembers, &bubble.CurrentMembers,
		&bubble.LifecycleStatus, &bubble.ExpiresAt, &bubble.CreatedAt)

	if err != nil {
		s.logger.Error("Failed to get bubble info", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get bubble info"})
		return
	}

	// Add user to bubble
	memberID := uuid.New().String()
	_, err = tx.Exec(c.Request.Context(), `
		INSERT INTO bubble_members (id, bubble_id, user_id, status, joined_at)
		VALUES ($1, $2, $3, 'active', $4)`,
		memberID, bubbleID, requesterID, time.Now())

	if err != nil {
		s.logger.Error("Failed to add bubble member", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept request"})
		return
	}

	// Update member count and calculate expiry extension
	newMemberCount := bubble.CurrentMembers + 1

	// Expiry extension logic: +30 days per new member (capped at 90 days from creation)
	maxExpiryDate := bubble.CreatedAt.AddDate(0, 0, 90) // 90 days from creation
	currentExpiry := bubble.ExpiresAt
	extendedExpiry := currentExpiry.AddDate(0, 0, 30) // +30 days

	// Use the earlier of extended expiry or max expiry
	var newExpiryDate time.Time
	if extendedExpiry.Before(maxExpiryDate) {
		newExpiryDate = extendedExpiry
	} else {
		newExpiryDate = maxExpiryDate
	}

	// Update bubble with new member count and expiry
	_, err = tx.Exec(c.Request.Context(), `
		UPDATE bubbles SET current_members = $1, expires_at = $2, updated_at = $3
		WHERE id = $4`,
		newMemberCount, newExpiryDate, time.Now(), bubbleID)

	if err != nil {
		s.logger.Error("Failed to update bubble", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update bubble"})
		return
	}

	// Log expiry extension if it occurred
	if newExpiryDate.After(currentExpiry) {
		s.logger.Info("Bubble expiry extended",
			zap.String("bubble_id", bubbleID),
			zap.String("requester_id", requesterID),
			zap.Time("old_expiry", currentExpiry),
			zap.Time("new_expiry", newExpiryDate),
			zap.Int("new_member_count", newMemberCount))
	}

	// Update request status
	_, err = tx.Exec(c.Request.Context(),
		"UPDATE bubble_requests SET status = 'accepted', updated_at = $1 WHERE id = $2",
		time.Now(), requestID)

	if err != nil {
		s.logger.Error("Failed to update request status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept request"})
		return
	}

	// Commit transaction
	if err := tx.Commit(c.Request.Context()); err != nil {
		s.logger.Error("Failed to commit transaction", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept request"})
		return
	}

	// Publish member joined event after successful transaction
	if err := s.publishMemberJoinedEvent(bubbleID, requesterID, memberID); err != nil {
		s.logger.Error("Failed to publish member joined event", zap.Error(err))
		// Don't fail the request since the transaction was successful
	}

	c.JSON(http.StatusOK, gin.H{"message": "Request accepted successfully"})
}

// declineBubbleRequest declines a pending bubble join request
func (s *Service) declineBubbleRequest(c *gin.Context) {
	requestID := c.Param("requestId")

	// Update request status
	_, err := s.db.Pool.Exec(c.Request.Context(),
		"UPDATE bubble_requests SET status = 'declined', updated_at = $1 WHERE id = $2",
		time.Now(), requestID)

	if err != nil {
		s.logger.Error("Failed to decline request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decline request"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Request declined successfully"})
}

// inviteToBubble handles inviting a user to a bubble
func (s *Service) inviteToBubble(c *gin.Context) {
	userID, _ := c.Get("user_id")
	bubbleID := c.Param("id")

	var req InviteToBubbleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create invite request
	requestID := uuid.New().String()
	_, err := s.db.Pool.Exec(c.Request.Context(), `
		INSERT INTO bubble_requests (id, bubble_id, requester_id, target_user_id, request_type, status, message, created_at, updated_at, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
		requestID, bubbleID, userID.(string), req.RecipientID, "invite", "pending", req.Message,
		time.Now(), time.Now(), time.Now().Add(7*24*time.Hour))

	if err != nil {
		s.logger.Error("Failed to create invite request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create invite request"})
		return
	}

	// Send notification to the invited user
	go func() {
		if err := s.notificationService.SendBubbleInviteNotification(context.Background(), req.RecipientID, userID.(string), bubbleID); err != nil {
			s.logger.Warn("Failed to send invite request notification",
				zap.String("recipient_id", req.RecipientID),
				zap.String("requester_id", userID.(string)),
				zap.Error(err))
		}
	}()

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Invite request created successfully",
		"request_id": requestID,
	})
}

// proposeUserToBubble handles proposing a user to join a bubble
func (s *Service) proposeUserToBubble(c *gin.Context) {
	userID, _ := c.Get("user_id")
	bubbleID := c.Param("id")

	var req ProposeUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create propose request
	requestID := uuid.New().String()
	_, err := s.db.Pool.Exec(c.Request.Context(), `
		INSERT INTO bubble_requests (id, bubble_id, requester_id, target_user_id, request_type, status, message, created_at, updated_at, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
		requestID, bubbleID, userID.(string), req.ProposedUserID, "propose", "pending", req.Message,
		time.Now(), time.Now(), time.Now().Add(7*24*time.Hour))

	if err != nil {
		s.logger.Error("Failed to create propose request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create propose request"})
		return
	}

	// Send notifications to all bubble members (except the requester)
	go func() {
		memberRows, err := s.db.Pool.Query(context.Background(), `
			SELECT user_id FROM bubble_members
			WHERE bubble_id = $1 AND status = 'active' AND user_id != $2`,
			bubbleID, userID.(string))

		if err == nil {
			defer memberRows.Close()

			for memberRows.Next() {
				var memberID string
				if err := memberRows.Scan(&memberID); err != nil {
					continue
				}

				if err := s.notificationService.SendBubbleProposeRequestNotification(context.Background(), memberID, userID.(string), bubbleID, req.ProposedUserID); err != nil {
					s.logger.Warn("Failed to send propose request notification",
						zap.String("member_id", memberID),
						zap.String("requester_id", userID.(string)),
						zap.String("proposed_user_id", req.ProposedUserID),
						zap.Error(err))
				}
			}
		}
	}()

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Propose request created successfully",
		"request_id": requestID,
	})
}

// kickoutFromBubble handles requesting to kick out a user from a bubble
func (s *Service) kickoutFromBubble(c *gin.Context) {
	userID, _ := c.Get("user_id")
	bubbleID := c.Param("id")

	var req struct {
		TargetUserID string `json:"target_user_id" binding:"required"`
		Message      string `json:"message"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create kickout request
	requestID := uuid.New().String()
	_, err := s.db.Pool.Exec(c.Request.Context(), `
		INSERT INTO bubble_requests (id, bubble_id, requester_id, target_user_id, request_type, status, message, created_at, updated_at, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
		requestID, bubbleID, userID.(string), req.TargetUserID, "kickout", "pending", req.Message,
		time.Now(), time.Now(), time.Now().Add(7*24*time.Hour))

	if err != nil {
		s.logger.Error("Failed to create kickout request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create kickout request"})
		return
	}

	// Send votekick initiated notifications to all bubble members (except requester and target)
	go func() {
		memberRows, err := s.db.Pool.Query(context.Background(), `
			SELECT user_id FROM bubble_members
			WHERE bubble_id = $1 AND status = 'active' AND user_id != $2 AND user_id != $3`,
			bubbleID, userID.(string), req.TargetUserID)

		if err == nil {
			defer memberRows.Close()

			for memberRows.Next() {
				var memberID string
				if err := memberRows.Scan(&memberID); err != nil {
					continue
				}

				if err := s.notificationService.SendBubbleVotekickInitiatedNotification(context.Background(), memberID, userID.(string), req.TargetUserID, bubbleID); err != nil {
					s.logger.Warn("Failed to send votekick initiated notification",
						zap.String("member_id", memberID),
						zap.String("initiator_id", userID.(string)),
						zap.String("target_user_id", req.TargetUserID),
						zap.Error(err))
				}
			}
		}
	}()

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Kickout request created successfully",
		"request_id": requestID,
	})
}

// createStartRequest handles creating a start request to begin a bubble with someone
func (s *Service) createStartRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req struct {
		TargetUserID string `json:"target_user_id" binding:"required"`
		Message      string `json:"message"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Use a transaction to ensure atomicity
	tx, err := s.db.Pool.Begin(c.Request.Context())
	if err != nil {
		s.logger.Error("Failed to begin transaction", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create start request"})
		return
	}
	defer tx.Rollback(c.Request.Context())

	// Check if a pending start request already exists between these users
	s.logger.Info("Checking for existing bubble start request",
		zap.String("requester_id", userID.(string)),
		zap.String("target_user_id", req.TargetUserID))

	var existingRequestID string
	err = tx.QueryRow(c.Request.Context(), `
		SELECT id FROM bubble_requests
		WHERE requester_id = $1 AND target_user_id = $2 AND request_type = 'start' AND status = 'pending'
		ORDER BY created_at DESC LIMIT 1`,
		userID.(string), req.TargetUserID).Scan(&existingRequestID)

	s.logger.Info("Duplicate check result",
		zap.String("requester_id", userID.(string)),
		zap.String("target_user_id", req.TargetUserID),
		zap.Error(err),
		zap.String("existing_request_id", existingRequestID))

	if err == nil {
		// Existing pending request found, return it instead of creating a new one
		s.logger.Info("Existing pending start request found",
			zap.String("requester_id", userID.(string)),
			zap.String("target_user_id", req.TargetUserID),
			zap.String("existing_request_id", existingRequestID))

		// Get the full request details to return
		var existingRequest struct {
			ID           string    `json:"id"`
			BubbleID     string    `json:"bubble_id"`
			RequesterID  string    `json:"requester_id"`
			RecipientID  string    `json:"recipient_id"`
			RequestType  string    `json:"request_type"`
			Status       string    `json:"status"`
			Message      string    `json:"message"`
			CreatedAt    time.Time `json:"created_at"`
			UpdatedAt    time.Time `json:"updated_at"`
			ExpiresAt    time.Time `json:"expires_at"`
		}

		err = tx.QueryRow(c.Request.Context(), `
			SELECT id, COALESCE(bubble_id, ''), requester_id, target_user_id, request_type, status,
				   COALESCE(message, ''), created_at, updated_at, expires_at
			FROM bubble_requests WHERE id = $1`,
			existingRequestID).Scan(
			&existingRequest.ID, &existingRequest.BubbleID, &existingRequest.RequesterID,
			&existingRequest.RecipientID, &existingRequest.RequestType, &existingRequest.Status,
			&existingRequest.Message, &existingRequest.CreatedAt, &existingRequest.UpdatedAt,
			&existingRequest.ExpiresAt)

		if err != nil {
			s.logger.Error("Failed to get existing request details", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get request details"})
			return
		}

		tx.Commit(c.Request.Context())
		c.JSON(http.StatusOK, existingRequest)
		return
	}

	// No existing request found, create a new one
	requestID := uuid.New().String()
	now := time.Now()
	expiresAt := now.Add(7 * 24 * time.Hour)

	_, err = tx.Exec(c.Request.Context(), `
		INSERT INTO bubble_requests (id, requester_id, target_user_id, request_type, status, message, created_at, updated_at, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
		requestID, userID.(string), req.TargetUserID, "start", "pending", req.Message,
		now, now, expiresAt)

	if err != nil {
		s.logger.Error("Failed to create start request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create start request"})
		return
	}

	// Commit the transaction
	if err = tx.Commit(c.Request.Context()); err != nil {
		s.logger.Error("Failed to commit transaction", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create start request"})
		return
	}

	// Update requester's pending sent bubble requests list
	if err := s.db.AddUserPendingSentBubbleRequest(c.Request.Context(), userID.(string), req.TargetUserID); err != nil {
		s.logger.Warn("Failed to update requester's pending sent bubble requests",
			zap.String("requester_id", userID.(string)),
			zap.String("target_user_id", req.TargetUserID),
			zap.Error(err))
		// Don't fail the request since the bubble request was created successfully
	}

	// Send notification to the target user
	go func() {
		if err := s.notificationService.SendBubbleStartRequestNotification(context.Background(), req.TargetUserID, userID.(string)); err != nil {
			s.logger.Warn("Failed to send start request notification",
				zap.String("target_user_id", req.TargetUserID),
				zap.String("requester_id", userID.(string)),
				zap.Error(err))
		}
	}()

	// Return the complete request data for frontend compatibility
	response := BubbleRequest{
		ID:          requestID,
		BubbleID:    "", // Empty for start requests since bubble doesn't exist yet
		RequesterID: userID.(string),
		RecipientID: req.TargetUserID,
		RequestType: "start",
		Status:      "pending",
		Message:     req.Message,
		CreatedAt:   now,
		UpdatedAt:   now,
		ExpiresAt:   expiresAt,
	}

	c.JSON(http.StatusCreated, response)
}

// acceptAnyRequest handles accepting any type of request
func (s *Service) acceptAnyRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("requestId")

	// Begin transaction
	tx, err := s.db.Pool.Begin(c.Request.Context())
	if err != nil {
		s.logger.Error("Failed to begin transaction", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	defer tx.Rollback(c.Request.Context())

	// Get request details
	var bubbleID, requesterID, targetUserID, requestType string
	err = tx.QueryRow(c.Request.Context(),
		"SELECT bubble_id, requester_id, target_user_id, request_type FROM bubble_requests WHERE id = $1 AND status = 'pending'",
		requestID).Scan(&bubbleID, &requesterID, &targetUserID, &requestType)

	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Request not found or not pending"})
		return
	}

	// Handle different request types
	var newMemberID string
	switch requestType {
	case "join":
		// Add user to bubble
		memberID := uuid.New().String()
		_, err = tx.Exec(c.Request.Context(), `
			INSERT INTO bubble_members (id, bubble_id, user_id, status, joined_at)
			VALUES ($1, $2, $3, 'active', $4)`,
			memberID, bubbleID, requesterID, time.Now())
		newMemberID = requesterID
	case "invite":
		// Add target user to bubble
		memberID := uuid.New().String()
		_, err = tx.Exec(c.Request.Context(), `
			INSERT INTO bubble_members (id, bubble_id, user_id, status, joined_at, invited_by)
			VALUES ($1, $2, $3, 'active', $4, $5)`,
			memberID, bubbleID, targetUserID, time.Now(), requesterID)
		newMemberID = targetUserID
	case "kickout":
		// Remove target user from bubble
		_, err = tx.Exec(c.Request.Context(), `
			UPDATE bubble_members SET status = 'removed', left_at = $1, updated_at = $2
			WHERE bubble_id = $3 AND user_id = $4 AND status = 'active'`,
			time.Now(), time.Now(), bubbleID, targetUserID)

		// Send votekick passed notifications to all remaining bubble members
		go func() {
			ctx := context.Background()
			memberRows, err := s.db.Pool.Query(ctx, `
				SELECT user_id FROM bubble_members
				WHERE bubble_id = $1 AND status = 'active' AND user_id != $2`,
				bubbleID, targetUserID)

			if err == nil {
				defer memberRows.Close()

				for memberRows.Next() {
					var memberID string
					if err := memberRows.Scan(&memberID); err != nil {
						continue
					}

					if err := s.notificationService.SendBubbleVotekickPassedNotification(ctx, memberID, targetUserID, bubbleID); err != nil {
						s.logger.Warn("Failed to send votekick passed notification",
							zap.String("member_id", memberID),
							zap.String("target_user_id", targetUserID),
							zap.Error(err))
					}
				}
			}
		}()
	case "propose":
		// Add proposed user to bubble
		memberID := uuid.New().String()
		_, err = tx.Exec(c.Request.Context(), `
			INSERT INTO bubble_members (id, bubble_id, user_id, status, joined_at, invited_by)
			VALUES ($1, $2, $3, 'active', $4, $5)`,
			memberID, bubbleID, targetUserID, time.Now(), requesterID)
		newMemberID = targetUserID
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Unknown request type"})
		return
	}

	if err != nil {
		s.logger.Error("Failed to process request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept request"})
		return
	}

	// Send notifications after successful request processing
	go func() {
		ctx := context.Background()

		// Send acceptance notification to requester
		if requestType == "join" {
			if err := s.notificationService.SendBubbleJoinRequestAcceptedNotification(ctx, requesterID, userID.(string), bubbleID); err != nil {
				s.logger.Warn("Failed to send join request accepted notification",
					zap.String("requester_id", requesterID),
					zap.String("accepted_by", userID.(string)),
					zap.Error(err))
			}
		}

		// Send member joined notifications to all existing members (except the new member)
		if newMemberID != "" && (requestType == "join" || requestType == "invite" || requestType == "propose") {
			memberRows, err := s.db.Pool.Query(ctx, `
				SELECT user_id FROM bubble_members
				WHERE bubble_id = $1 AND status = 'active' AND user_id != $2`,
				bubbleID, newMemberID)

			if err == nil {
				defer memberRows.Close()

				for memberRows.Next() {
					var memberID string
					if err := memberRows.Scan(&memberID); err != nil {
						continue
					}

					if err := s.notificationService.SendBubbleMemberJoinedNotification(ctx, memberID, newMemberID, bubbleID); err != nil {
						s.logger.Warn("Failed to send member joined notification",
							zap.String("member_id", memberID),
							zap.String("new_member_id", newMemberID),
							zap.Error(err))
					}
				}
			}
		}
	}()

	// Update request status
	_, err = tx.Exec(c.Request.Context(),
		"UPDATE bubble_requests SET status = 'accepted', updated_at = $1 WHERE id = $2",
		time.Now(), requestID)

	if err != nil {
		s.logger.Error("Failed to update request status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept request"})
		return
	}

	// Commit transaction
	if err = tx.Commit(c.Request.Context()); err != nil {
		s.logger.Error("Failed to commit transaction", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept request"})
		return
	}

	// Remove from requester's pending sent bubble requests list (outside transaction)
	if requestType == "start" && targetUserID != "" {
		if err := s.db.RemoveUserPendingSentBubbleRequest(c.Request.Context(), requesterID, targetUserID); err != nil {
			s.logger.Warn("Failed to remove from requester's pending sent bubble requests",
				zap.String("requester_id", requesterID),
				zap.String("target_user_id", targetUserID),
				zap.Error(err))
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": "Request accepted successfully"})
}

// declineAnyRequest handles declining any type of request
func (s *Service) declineAnyRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("requestId")

	// Get request details before updating
	var requesterID, bubbleID, requestType string
	err := s.db.Pool.QueryRow(c.Request.Context(), `
		SELECT requester_id, bubble_id, request_type
		FROM bubble_requests
		WHERE id = $1 AND status = 'pending'`,
		requestID).Scan(&requesterID, &bubbleID, &requestType)

	if err != nil {
		s.logger.Error("Failed to get request details", zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "Request not found"})
		return
	}

	// Update request status
	_, err = s.db.Pool.Exec(c.Request.Context(),
		"UPDATE bubble_requests SET status = 'declined', updated_at = $1 WHERE id = $2 AND status = 'pending'",
		time.Now(), requestID)

	if err != nil {
		s.logger.Error("Failed to decline request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decline request"})
		return
	}

	// Remove from requester's pending sent bubble requests list
	if requestType == "start" {
		// For start requests, get target user ID from the request
		var targetUserID string
		err := s.db.Pool.QueryRow(c.Request.Context(),
			"SELECT target_user_id FROM bubble_requests WHERE id = $1",
			requestID).Scan(&targetUserID)

		if err == nil && targetUserID != "" {
			if err := s.db.RemoveUserPendingSentBubbleRequest(c.Request.Context(), requesterID, targetUserID); err != nil {
				s.logger.Warn("Failed to remove from requester's pending sent bubble requests",
					zap.String("requester_id", requesterID),
					zap.String("target_user_id", targetUserID),
					zap.Error(err))
			}
		}
	}

	// Send rejection notification to requester
	go func() {
		ctx := context.Background()

		if requestType == "join" {
			if err := s.notificationService.SendBubbleJoinRequestRejectedNotification(ctx, requesterID, userID.(string), bubbleID); err != nil {
				s.logger.Warn("Failed to send join request rejected notification",
					zap.String("requester_id", requesterID),
					zap.String("rejected_by", userID.(string)),
					zap.Error(err))
			}
		}
	}()

	c.JSON(http.StatusOK, gin.H{"message": "Request declined successfully"})
}

// initializeJetStream initializes JetStream context
func (s *Service) initializeJetStream() error {
	if s.natsConn == nil {
		s.logger.Warn("NATS connection not available, skipping JetStream initialization")
		return nil
	}

	js, err := s.natsConn.JetStream()
	if err != nil {
		return fmt.Errorf("failed to create JetStream context: %w", err)
	}
	s.js = js

	// Create or update the bubble events stream
	streamConfig := &nats.StreamConfig{
		Name:        "BUBBLE_EVENTS",
		Description: "Stream for bubble membership events",
		Subjects:    []string{"events.bubble.>"},
		Retention:   nats.WorkQueuePolicy,
		MaxAge:      24 * time.Hour, // Keep events for 24 hours
		Storage:     nats.FileStorage,
		Replicas:    1,
	}

	_, err = js.AddStream(streamConfig)
	if err != nil && err != nats.ErrStreamNameAlreadyInUse {
		return fmt.Errorf("failed to create bubble events stream: %w", err)
	}

	s.logger.Info("JetStream initialized successfully for bubble service")
	// Start expiry consumer after stream init
	s.startExpiryConsumer()

	// Start expiry reminder consumer
	s.startExpiryReminderConsumer()

	return nil
}

// publishMemberJoinedEvent publishes a member joined event to NATS
func (s *Service) publishMemberJoinedEvent(bubbleID, userID, memberID string) error {
	if s.js == nil {
		s.logger.Warn("JetStream not available, skipping event publishing")
		return nil
	}

	event := BubbleMemberJoinedEvent{
		EventType: "bubble.member_joined",
		BubbleID:  bubbleID,
		UserID:    userID,
		MemberID:  memberID,
		Timestamp: time.Now().Unix(),
	}

	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal member joined event: %w", err)
	}

	_, err = s.js.Publish("events.bubble.member_joined", data)
	if err != nil {
		return fmt.Errorf("failed to publish member joined event: %w", err)
	}

	s.logger.Info("Published member joined event",
		zap.String("bubble_id", bubbleID),
		zap.String("user_id", userID),
		zap.String("member_id", memberID))

	return nil
}

// publishMemberLeftEvent publishes a member left event to NATS
func (s *Service) publishMemberLeftEvent(bubbleID, userID, reason string) error {
	if s.js == nil {
		s.logger.Warn("JetStream not available, skipping event publishing")
		return nil
	}

	event := BubbleMemberLeftEvent{
		EventType: "bubble.member_left",
		BubbleID:  bubbleID,
		UserID:    userID,
		Reason:    reason,
		Timestamp: time.Now().Unix(),
	}

	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal member left event: %w", err)
	}

	_, err = s.js.Publish("events.bubble.member_left", data)
	if err != nil {
		return fmt.Errorf("failed to publish member left event: %w", err)
	}

	s.logger.Info("Published member left event",
		zap.String("bubble_id", bubbleID),
		zap.String("user_id", userID),
		zap.String("reason", reason))

	return nil
}

// scheduleExpiryEvent publishes a delayed message via JetStream using NATS Cron headers
func (s *Service) scheduleExpiryEvent(bubbleID string, expiresAt time.Time) error {
	if s.js == nil {
		return nil // JetStream not configured
	}

	event := BubbleExpireEvent{
		EventType: "bubble.expire",
		BubbleID:  bubbleID,
		Timestamp: expiresAt.Unix(),
	}
	data, err := json.Marshal(event)
	if err != nil {
		return err
	}

	hdr := nats.Header{}
	hdr.Set("Nats-Not-Before", expiresAt.UTC().Format(time.RFC3339))

	msg := &nats.Msg{
		Subject: "events.bubble.expire",
		Header:  hdr,
		Data:    data,
	}

	_, err = s.js.PublishMsg(msg)
	return err
}

// startExpiryConsumer subscribes to expiry events and marks bubbles as expired
func (s *Service) startExpiryConsumer() {
	if s.js == nil {
		return
	}

	sub, err := s.js.PullSubscribe("events.bubble.expire", "bubble-expiry-consumer")
	if err != nil {
		s.logger.Error("Failed to subscribe to expiry events", zap.Error(err))
		return
	}

	go func() {
		for {
			msgs, err := sub.Fetch(10, nats.MaxWait(5*time.Second))
			if err != nil {
				if err == nats.ErrTimeout {
					continue
				}
				s.logger.Error("Expiry fetch error", zap.Error(err))
				time.Sleep(5 * time.Second)
				continue
			}

			for _, m := range msgs {
				s.handleExpiryMsg(m)
			}
		}
	}()
}

// handleExpiryMsg processes a single expiry message
func (s *Service) handleExpiryMsg(msg *nats.Msg) {
	var ev BubbleExpireEvent
	if err := json.Unmarshal(msg.Data, &ev); err != nil {
		s.logger.Error("Failed to unmarshal expiry event", zap.Error(err))
		_ = msg.Term()
		return
	}

	ctx := context.Background()

	// Get bubble members before expiring the bubble
	memberRows, err := s.db.Pool.Query(ctx, `
		SELECT user_id FROM bubble_members
		WHERE bubble_id = $1 AND status = 'active'`, ev.BubbleID)
	if err != nil {
		s.logger.Error("Failed to get bubble members for expiry", zap.String("bubble_id", ev.BubbleID), zap.Error(err))
		_ = msg.Nak()
		return
	}
	defer memberRows.Close()

	var members []string
	for memberRows.Next() {
		var memberID string
		if err := memberRows.Scan(&memberID); err != nil {
			continue
		}
		members = append(members, memberID)
	}

	// Get bubble name before updating status
	var bubbleName string
	err = s.db.Pool.QueryRow(ctx, `SELECT name FROM bubbles WHERE id = $1`, ev.BubbleID).Scan(&bubbleName)
	if err != nil {
		bubbleName = "Unknown Bubble" // Default fallback
	}

	// Update bubble status to expired
	expiredAt := time.Now()
	_, err = s.db.Pool.Exec(ctx, `UPDATE bubbles SET status='expired', updated_at=$1 WHERE id=$2 AND status='active'`, expiredAt, ev.BubbleID)
	if err != nil {
		s.logger.Error("Failed to expire bubble", zap.String("bubble_id", ev.BubbleID), zap.Error(err))
		_ = msg.Nak()
		return
	}

	// Send bubble popped notifications to all members
	for _, memberID := range members {
		if err := s.notificationService.SendBubblePoppedNotification(ctx, memberID, ev.BubbleID, bubbleName, expiredAt); err != nil {
			s.logger.Warn("Failed to send bubble popped notification",
				zap.String("member_id", memberID),
				zap.String("bubble_id", ev.BubbleID),
				zap.Error(err))
		}
	}

	// Publish bubble expired event for friendship service
	if len(members) > 1 { // Only create friend requests if there are multiple members
		expiredEvent := BubbleExpiredEvent{
			BubbleID:  ev.BubbleID,
			Members:   members,
			ExpiredAt: time.Now().Unix(),
		}

		if err := s.publishBubbleExpiredEvent(expiredEvent); err != nil {
			s.logger.Error("Failed to publish bubble expired event",
				zap.String("bubble_id", ev.BubbleID),
				zap.Error(err))
			// Don't fail the expiry process if event publishing fails
		}
	}

	s.logger.Info("Bubble expired successfully",
		zap.String("bubble_id", ev.BubbleID),
		zap.Int("member_count", len(members)))

	_ = msg.Ack()
}

// publishBubbleExpiredEvent publishes a bubble expired event to NATS
func (s *Service) publishBubbleExpiredEvent(event BubbleExpiredEvent) error {
	if s.natsConn == nil {
		s.logger.Warn("NATS connection not available, skipping bubble expired event publishing")
		return nil
	}

	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal bubble expired event: %w", err)
	}

	err = s.natsConn.Publish("events.bubble.expired", data)
	if err != nil {
		return fmt.Errorf("failed to publish bubble expired event: %w", err)
	}

	s.logger.Info("Bubble expired event published successfully",
		zap.String("bubble_id", event.BubbleID),
		zap.Strings("members", event.Members))

	return nil
}

// scheduleExpiryReminders schedules reminder notifications for bubble expiry
func (s *Service) scheduleExpiryReminders(bubbleID string, expiresAt time.Time) error {
	if s.js == nil {
		return nil // JetStream not configured
	}

	// Schedule reminders for 60, 30, 20, 10, 7, 3 days, and 24 hours before expiry
	reminderDays := []int{60, 30, 20, 10, 7, 3, 1}

	for _, days := range reminderDays {
		reminderTime := expiresAt.AddDate(0, 0, -days)

		// Only schedule if reminder time is in the future
		if reminderTime.After(time.Now()) {
			event := BubbleExpiryReminderEvent{
				EventType: "bubble.expiry.reminder",
				BubbleID:  bubbleID,
				DaysLeft:  days,
				Timestamp: reminderTime.Unix(),
			}

			data, err := json.Marshal(event)
			if err != nil {
				s.logger.Error("Failed to marshal expiry reminder event", zap.Error(err))
				continue
			}

			hdr := nats.Header{}
			hdr.Set("Nats-Not-Before", reminderTime.UTC().Format(time.RFC3339))

			msg := &nats.Msg{
				Subject: "events.bubble.expiry.reminder",
				Header:  hdr,
				Data:    data,
			}

			if _, err := s.js.PublishMsg(msg); err != nil {
				s.logger.Error("Failed to schedule expiry reminder",
					zap.String("bubble_id", bubbleID),
					zap.Int("days_left", days),
					zap.Error(err))
			} else {
				s.logger.Info("Scheduled expiry reminder",
					zap.String("bubble_id", bubbleID),
					zap.Int("days_left", days),
					zap.Time("reminder_time", reminderTime))
			}
		}
	}

	return nil
}

// startExpiryReminderConsumer subscribes to expiry reminder events
func (s *Service) startExpiryReminderConsumer() {
	if s.js == nil {
		return
	}

	sub, err := s.js.PullSubscribe("events.bubble.expiry.reminder", "bubble-expiry-reminder-consumer")
	if err != nil {
		s.logger.Error("Failed to subscribe to expiry reminder events", zap.Error(err))
		return
	}

	go func() {
		for {
			msgs, err := sub.Fetch(10, nats.MaxWait(5*time.Second))
			if err != nil {
				if err == nats.ErrTimeout {
					continue
				}
				s.logger.Error("Expiry reminder fetch error", zap.Error(err))
				time.Sleep(5 * time.Second)
				continue
			}

			for _, m := range msgs {
				s.handleExpiryReminderMsg(m)
			}
		}
	}()
}

// handleExpiryReminderMsg processes a single expiry reminder message
func (s *Service) handleExpiryReminderMsg(msg *nats.Msg) {
	var ev BubbleExpiryReminderEvent
	if err := json.Unmarshal(msg.Data, &ev); err != nil {
		s.logger.Error("Failed to unmarshal expiry reminder event", zap.Error(err))
		_ = msg.Term()
		return
	}

	ctx := context.Background()

	// Check if bubble still exists and is active
	var bubbleExists bool
	err := s.db.Pool.QueryRow(ctx, `SELECT EXISTS(SELECT 1 FROM bubbles WHERE id = $1 AND status = 'active')`, ev.BubbleID).Scan(&bubbleExists)
	if err != nil {
		s.logger.Error("Failed to check bubble existence", zap.String("bubble_id", ev.BubbleID), zap.Error(err))
		_ = msg.Nak()
		return
	}

	if !bubbleExists {
		s.logger.Info("Bubble no longer exists or is not active, skipping reminder", zap.String("bubble_id", ev.BubbleID))
		_ = msg.Ack()
		return
	}

	// Get bubble members to send notifications
	memberRows, err := s.db.Pool.Query(ctx, `
		SELECT user_id FROM bubble_members
		WHERE bubble_id = $1 AND status = 'active'`, ev.BubbleID)
	if err != nil {
		s.logger.Error("Failed to get bubble members for expiry reminder", zap.String("bubble_id", ev.BubbleID), zap.Error(err))
		_ = msg.Nak()
		return
	}
	defer memberRows.Close()

	var notificationsSent int
	for memberRows.Next() {
		var memberID string
		if err := memberRows.Scan(&memberID); err != nil {
			continue
		}

		// Send expiry notification to member
		if err := s.notificationService.SendBubbleExpiryNotification(ctx, memberID, ev.BubbleID, ev.DaysLeft); err != nil {
			s.logger.Warn("Failed to send expiry reminder notification",
				zap.String("member_id", memberID),
				zap.String("bubble_id", ev.BubbleID),
				zap.Int("days_left", ev.DaysLeft),
				zap.Error(err))
		} else {
			notificationsSent++
		}
	}

	s.logger.Info("Expiry reminder notifications sent",
		zap.String("bubble_id", ev.BubbleID),
		zap.Int("days_left", ev.DaysLeft),
		zap.Int("notifications_sent", notificationsSent))

	_ = msg.Ack()
}

// getUserPendingRequests handles getting all pending requests for a user
func (s *Service) getUserPendingRequests(c *gin.Context) {
	userID, _ := c.Get("user_id")

	// Get all pending requests where user is the recipient (target_user_id)
	// This includes invite, propose, and start requests
	rows, err := s.db.Pool.Query(c.Request.Context(), `
		SELECT br.id, br.bubble_id, br.requester_id, br.target_user_id, br.request_type,
		       br.status, br.message, br.created_at, br.updated_at, br.expires_at,
		       u.username, u.first_name, u.last_name,
		       b.name as bubble_name
		FROM bubble_requests br
		JOIN users u ON br.requester_id = u.id
		LEFT JOIN bubbles b ON br.bubble_id = b.id
		WHERE (br.target_user_id = $1 OR
		       (br.request_type = 'join' AND br.bubble_id IN (
		           SELECT bubble_id FROM bubble_members WHERE user_id = $1 AND status = 'active'
		       )))
		AND br.status = 'pending'
		AND br.expires_at > NOW()
		ORDER BY br.created_at DESC`,
		userID.(string))

	if err != nil {
		s.logger.Error("Failed to get user pending requests", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get pending requests"})
		return
	}
	defer rows.Close()

	var requests []BubbleRequest
	for rows.Next() {
		var req BubbleRequest
		var firstName, lastName *string
		var bubbleName *string
		var targetUserID *string
		err := rows.Scan(&req.ID, &req.BubbleID, &req.RequesterID, &targetUserID, &req.RequestType,
			&req.Status, &req.Message, &req.CreatedAt, &req.UpdatedAt, &req.ExpiresAt,
			&req.Username, &firstName, &lastName, &bubbleName)
		if err != nil {
			s.logger.Error("Failed to scan bubble request", zap.Error(err))
			continue
		}

		if firstName != nil {
			req.FirstName = *firstName
		}
		if lastName != nil {
			req.LastName = *lastName
		}
		if targetUserID != nil {
			req.RecipientID = *targetUserID
		}

		requests = append(requests, req)
	}

	c.JSON(http.StatusOK, gin.H{
		"requests": requests,
		"count":    len(requests),
	})
}

// getBubbleBadgeStatus handles getting bubble badge status for multiple users
// This endpoint is optimized for performance using the denormalized member_count
func (s *Service) getBubbleBadgeStatus(c *gin.Context) {
	var request struct {
		UserIDs []string `json:"user_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate user IDs
	if len(request.UserIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_ids cannot be empty"})
		return
	}

	if len(request.UserIDs) > 100 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "too many user_ids, maximum 100 allowed"})
		return
	}

	// Validate UUID format
	for _, userID := range request.UserIDs {
		if _, err := uuid.Parse(userID); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid user_id format: %s", userID)})
			return
		}
	}

	// Use the optimized database function
	results, err := s.db.GetBubbleBadgeStatusForUsers(c.Request.Context(), request.UserIDs)
	if err != nil {
		s.logger.Error("Failed to get bubble badge status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get bubble badge status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"bubble_badge_statuses": results,
		"count":                 len(results),
	})
}

// getSingleUserBubbleBadgeStatus handles getting bubble badge status for a single user
func (s *Service) getSingleUserBubbleBadgeStatus(c *gin.Context) {
	userID := c.Param("user_id")

	// Validate user ID
	if _, err := uuid.Parse(userID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user_id format"})
		return
	}

	// Use the optimized database function
	status, err := s.db.GetBubbleBadgeStatusForUser(c.Request.Context(), userID)
	if err != nil {
		s.logger.Error("Failed to get user bubble badge status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get bubble badge status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user_id": userID,
		"status":  status,
	})
}
