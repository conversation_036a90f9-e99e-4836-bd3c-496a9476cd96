package contact

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nats-io/nats.go"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"go.uber.org/zap"

	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
)

// Service handles contact operations using PostgreSQL
type Service struct {
	logger      *zap.Logger
	repository  *PostgreSQLRepository
	db          *database.PostgreSQLClient
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
	httpClient  *http.Client
	natsConn    *nats.Conn
	mqttClient  mqtt.Client
}

// Dependencies holds the dependencies for the contact service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
	NATSConn    *nats.Conn
	MQTTClient  mqtt.Client
}

// New creates a new contact service instance
func New(deps *Dependencies) *Service {
	repository := NewPostgreSQLRepository(deps.DB.Pool, deps.Logger)

	return &Service{
		logger:      deps.Logger,
		repository:  repository,
		db:          deps.DB,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
		httpClient:  &http.Client{Timeout: 10 * time.Second},
		natsConn:    deps.NATSConn,
		mqttClient:  deps.MQTTClient,
	}
}

// RegisterRoutes registers the contact service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	router.POST("/requests", s.authMiddleware(), s.sendContactRequest)
	router.POST("/requests/:id/accept", s.authMiddleware(), s.acceptContactRequest)
	router.POST("/requests/:id/decline", s.authMiddleware(), s.declineContactRequest)
	router.GET("/requests/:id", s.authMiddleware(), s.getContactRequest)
	router.GET("/requests/history", s.authMiddleware(), s.getContactRequestHistory)
	router.POST("/requests/expire-old", s.authMiddleware(), s.expireOldRequests)
	router.DELETE("/requests/:id", s.authMiddleware(), s.cancelContactRequest)
	router.GET("/contacts", s.authMiddleware(), s.getContacts)
	router.GET("/contacts/mutual", s.authMiddleware(), s.getMutualContacts)
	router.GET("/contacts/suggestions", s.authMiddleware(), s.getContactSuggestions)
	router.GET("/requests/sent", s.authMiddleware(), s.getSentRequests)
	router.GET("/requests/received", s.authMiddleware(), s.getReceivedRequests)
	router.DELETE("/:contactId", s.authMiddleware(), s.removeContact)
}

// Note: Contact struct is now defined in postgres_repository.go

// ContactRequest represents a contact request payload
type ContactRequest struct {
	RecipientID string  `json:"recipient_id" binding:"required"`
	Message     *string `json:"message,omitempty"` // Optional message with the request
}

// ContactResponse represents a contact in API responses
type ContactResponse struct {
	ID              string    `json:"id"`
	SenderId        string    `json:"senderId"`
	SenderName      string    `json:"senderName"`
	SenderAvatarUrl *string   `json:"senderAvatarUrl,omitempty"`
	ReceiverId      string    `json:"receiverId"`
	ReceiverName    string    `json:"receiverName"`
	ReceiverAvatarUrl *string `json:"receiverAvatarUrl,omitempty"`
	SentAt          time.Time `json:"sentAt"`
	Status          string    `json:"status"`
	Message         *string   `json:"message,omitempty"`
	RespondedAt     *time.Time `json:"respondedAt,omitempty"`
}

// ContactRequestEvent represents MQTT events for contact requests
type ContactRequestEvent struct {
	EventType   string    `json:"eventType"`
	EventID     string    `json:"eventId"`
	UserID      string    `json:"userId"`
	RequestID   string    `json:"requestId"`
	Action      string    `json:"action"`
	SenderID    string    `json:"senderId"`
	ReceiverID  string    `json:"receiverId"`
	SenderName  string    `json:"senderName,omitempty"`
	Message     string    `json:"message,omitempty"`
	Timestamp   time.Time `json:"timestamp"`
}

// sendContactRequest handles sending a contact request
func (s *Service) sendContactRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req ContactRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate recipient ID
	if _, err := uuid.Parse(req.RecipientID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recipient ID format"})
		return
	}

	// Check if user is trying to add themselves
	if userID.(string) == req.RecipientID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot send contact request to yourself"})
		return
	}

	// Rate limiting for contact requests
	allowed, err := s.rateLimiter.AllowSocialOperation(c.Request.Context(), userID.(string), "contact_request")
	if err != nil {
		s.logger.Error("Rate limit check failed", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !allowed {
		c.JSON(http.StatusTooManyRequests, gin.H{"error": "Contact request rate limit exceeded"})
		return
	}

	// Check if contact relationship already exists
	exists, err := s.repository.ContactRelationshipExists(c.Request.Context(), userID.(string), req.RecipientID)
	if err != nil {
		s.logger.Error("Failed to check existing contact relationship", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if exists {
		c.JSON(http.StatusConflict, gin.H{"error": "Contact relationship already exists"})
		return
	}

	// Create contact request
	contact := &Contact{
		RequesterID: userID.(string),
		RecipientID: req.RecipientID,
		Status:      "pending",
		Message:     req.Message,
	}

	if err := s.repository.CreateContact(c.Request.Context(), contact); err != nil {
		s.logger.Error("Failed to create contact request",
			zap.String("requester_id", userID.(string)),
			zap.String("recipient_id", req.RecipientID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send contact request"})
		return
	}

	// Send notification to recipient
	if err := s.sendContactRequestNotification(c.Request.Context(), req.RecipientID, userID.(string)); err != nil {
		s.logger.Warn("Failed to send contact request notification",
			zap.String("requester_id", userID.(string)),
			zap.String("recipient_id", req.RecipientID),
			zap.Error(err))
		// Don't fail the request if notification fails
	}

	s.logger.Info("Contact request sent successfully",
		zap.String("requester_id", userID.(string)),
		zap.String("recipient_id", req.RecipientID))

	// Return the created contact request with real user data
	contactResponse, err := s.buildContactResponse(c.Request.Context(), contact)
	if err != nil {
		s.logger.Error("Failed to build contact response", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create contact response"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Contact request sent successfully",
		"request": contactResponse,
	})
}

// acceptContactRequest handles accepting a contact request
func (s *Service) acceptContactRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	// Get the contact request
	contact, err := s.repository.GetContactByID(c.Request.Context(), requestID)
	if err != nil {
		s.logger.Error("Failed to get contact request",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact request not found"})
		return
	}

	// Verify user is the recipient
	if contact.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only accept requests sent to you"})
		return
	}

	// Verify request is pending
	if contact.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact request is not pending"})
		return
	}

	// Update contact status to accepted
	if err := s.repository.UpdateContactStatus(c.Request.Context(), requestID, "accepted"); err != nil {
		s.logger.Error("Failed to accept contact request",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept contact request"})
		return
	}

	// Send notification to requester about acceptance
	if err := s.sendContactRequestAcceptedNotification(c.Request.Context(), contact.RequesterID, contact.RecipientID); err != nil {
		s.logger.Warn("Failed to send contact request accepted notification",
			zap.String("requester_id", contact.RequesterID),
			zap.String("recipient_id", contact.RecipientID),
			zap.Error(err))
	}



	s.logger.Info("Contact request accepted successfully",
		zap.String("request_id", requestID),
		zap.String("requester_id", contact.RequesterID),
		zap.String("recipient_id", contact.RecipientID))

	c.JSON(http.StatusOK, gin.H{"message": "Contact request accepted successfully"})
}

// declineContactRequest handles declining a contact request
func (s *Service) declineContactRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	// Get the contact request
	contact, err := s.repository.GetContactByID(c.Request.Context(), requestID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact request not found"})
		return
	}

	// Verify user is the recipient
	if contact.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only decline requests sent to you"})
		return
	}

	// Verify request is pending
	if contact.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact request is not pending"})
		return
	}

	// Update contact status to declined
	if err := s.repository.UpdateContactStatus(c.Request.Context(), requestID, "declined"); err != nil {
		s.logger.Error("Failed to decline contact request",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decline contact request"})
		return
	}

	// Send notification to requester about decline
	if err := s.sendContactRequestDeclinedNotification(c.Request.Context(), contact.RequesterID, contact.RecipientID); err != nil {
		s.logger.Warn("Failed to send contact request declined notification",
			zap.String("requester_id", contact.RequesterID),
			zap.String("recipient_id", contact.RecipientID),
			zap.Error(err))
	}

	s.logger.Info("Contact request declined successfully",
		zap.String("request_id", requestID),
		zap.String("requester_id", contact.RequesterID),
		zap.String("recipient_id", contact.RecipientID))

	c.JSON(http.StatusOK, gin.H{"message": "Contact request declined successfully"})
}

// getContacts handles getting user's accepted contacts
func (s *Service) getContacts(c *gin.Context) {
	userID, _ := c.Get("user_id")

	contacts, err := s.repository.GetContactsByUser(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get contacts",
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get contacts"})
		return
	}

	// Convert to response format
	contactResponses := make([]*ContactResponse, len(contacts))
	for i, contact := range contacts {
		contactResponses[i] = &ContactResponse{
			ID:         contact.ID,
			SenderId:   contact.RequesterID,
			ReceiverId: contact.RecipientID,
			Status:     contact.Status,
			SentAt:     contact.CreatedAt,
			RespondedAt: contact.AcceptedAt,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"contacts": contactResponses,
		"count":    len(contactResponses),
	})
}

// getSentRequests handles getting user's sent contact requests
func (s *Service) getSentRequests(c *gin.Context) {
	userID, _ := c.Get("user_id")

	contacts, err := s.repository.GetSentContactRequests(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get sent requests", 
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get sent requests"})
		return
	}

	// Convert to response format with real user data
	requestResponses := make([]*ContactResponse, len(contacts))
	for i, contact := range contacts {
		response, err := s.buildContactResponse(c.Request.Context(), contact)
		if err != nil {
			s.logger.Error("Failed to build contact response",
				zap.String("contact_id", contact.ID),
				zap.Error(err))
			// Continue with next contact instead of failing the entire request
			continue
		}
		requestResponses[i] = response
	}

	c.JSON(http.StatusOK, gin.H{
		"requests": requestResponses,
		"count":    len(requestResponses),
	})
}

// getReceivedRequests handles getting user's received contact requests
func (s *Service) getReceivedRequests(c *gin.Context) {
	userID, _ := c.Get("user_id")

	contacts, err := s.repository.GetReceivedContactRequests(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get received requests", 
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get received requests"})
		return
	}

	// Convert to response format with real user data
	requestResponses := make([]*ContactResponse, len(contacts))
	for i, contact := range contacts {
		response, err := s.buildContactResponse(c.Request.Context(), contact)
		if err != nil {
			s.logger.Error("Failed to build contact response",
				zap.String("contact_id", contact.ID),
				zap.Error(err))
			// Continue with next contact instead of failing the entire request
			continue
		}
		requestResponses[i] = response
	}

	c.JSON(http.StatusOK, gin.H{
		"requests": requestResponses,
		"count":    len(requestResponses),
	})
}

// removeContact handles removing a contact relationship
func (s *Service) removeContact(c *gin.Context) {
	userID, _ := c.Get("user_id")
	contactID := c.Param("contactId")

	if contactID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact ID is required"})
		return
	}

	// Get the contact to verify ownership
	contact, err := s.repository.GetContactByID(c.Request.Context(), contactID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact not found"})
		return
	}

	// Verify user is part of this contact relationship
	if contact.RequesterID != userID.(string) && contact.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Cannot remove contact relationship you're not part of"})
		return
	}

	// Remove the contact
	if err := s.repository.DeleteContact(c.Request.Context(), contactID); err != nil {
		s.logger.Error("Failed to remove contact",
			zap.String("contact_id", contactID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove contact"})
		return
	}

	s.logger.Info("Contact removed successfully", 
		zap.String("contact_id", contactID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Contact removed successfully"})
}

// getContactRequest handles getting a specific contact request
func (s *Service) getContactRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	contact, err := s.repository.GetContactByID(c.Request.Context(), requestID)
	if err != nil {
		database.HandleContactError(c, err, "get contact request")
		return
	}

	// Verify user is involved in this request
	if contact.RequesterID != userID.(string) && contact.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	response := &ContactResponse{
		ID:              contact.ID,
		SenderId:        contact.RequesterID,
		ReceiverId:      contact.RecipientID,
		Status:          contact.Status,
		SentAt:          contact.CreatedAt,
		RespondedAt:     contact.AcceptedAt,
	}

	c.JSON(http.StatusOK, gin.H{"request": response})
}

// getContactRequestHistory handles getting contact request history
func (s *Service) getContactRequestHistory(c *gin.Context) {
	userID, _ := c.Get("user_id")

	contacts, err := s.repository.GetContactsByUser(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get contact request history",
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get request history"})
		return
	}

	// Convert to response format
	requestResponses := make([]*ContactResponse, len(contacts))
	for i, contact := range contacts {
		requestResponses[i] = &ContactResponse{
			ID:              contact.ID,
			SenderId:        contact.RequesterID,
			ReceiverId:      contact.RecipientID,
			Status:          contact.Status,
			SentAt:          contact.CreatedAt,
			RespondedAt:     contact.AcceptedAt,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"requests": requestResponses,
		"count":    len(requestResponses),
	})
}

// expireOldRequests handles expiring old contact requests
func (s *Service) expireOldRequests(c *gin.Context) {
	// Expire requests older than 30 days
	olderThan := time.Now().AddDate(0, 0, -30)

	expiredCount, err := s.repository.ExpireOldRequests(c.Request.Context(), olderThan)
	if err != nil {
		s.logger.Error("Failed to expire old requests", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to expire old requests"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Old requests expired successfully",
		"expired_count": expiredCount,
	})
}

// cancelContactRequest handles canceling a contact request
func (s *Service) cancelContactRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	// Get the contact request
	contact, err := s.repository.GetContactByID(c.Request.Context(), requestID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact request not found"})
		return
	}

	// Verify user is the requester
	if contact.RequesterID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only cancel your own requests"})
		return
	}

	// Verify request is still pending
	if contact.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Can only cancel pending requests"})
		return
	}

	// Delete the contact request (cancellation = deletion)
	if err := s.repository.DeleteContact(c.Request.Context(), requestID); err != nil {
		s.logger.Error("Failed to cancel contact request",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel request"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Contact request cancelled successfully"})
}

// getMutualContacts handles getting mutual contacts between users
func (s *Service) getMutualContacts(c *gin.Context) {
	userID, _ := c.Get("user_id")
	targetUserID := c.Query("userId")

	if targetUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Target user ID is required"})
		return
	}

	// Get mutual contact IDs using repository
	mutualContactIDs, err := s.repository.GetMutualContacts(c.Request.Context(), userID.(string), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get mutual contacts", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get mutual contacts"})
		return
	}

	// Fetch user data for mutual contacts
	var contacts []map[string]interface{}
	for _, contactID := range mutualContactIDs {
		userData, err := s.fetchUserData(c.Request.Context(), contactID)
		if err != nil {
			s.logger.Warn("Failed to fetch user data for mutual contact",
				zap.String("contact_id", contactID),
				zap.Error(err))
			continue
		}

		contacts = append(contacts, map[string]interface{}{
			"id":           userData.ID,
			"username":     userData.Username,
			"first_name":   userData.FirstName,
			"last_name":    userData.LastName,
			"display_name": userData.FirstName + " " + userData.LastName,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"contacts": contacts,
		"count": len(contacts),
	})
}

// getContactSuggestions handles getting contact suggestions
func (s *Service) getContactSuggestions(c *gin.Context) {
	// For now, return empty suggestions since this requires complex logic
	// that would be better implemented as a separate analytics service
	// TODO: Implement contact suggestions based on mutual contacts, shared bubbles, etc.

	c.JSON(http.StatusOK, gin.H{
		"suggestions": []map[string]interface{}{},
		"count": 0,
		"message": "Contact suggestions feature will be implemented in a future update",
	})
}

// Helper methods

// Note: contactRelationshipExists, getContactByID, and updateContact methods
// are now handled directly by the PostgreSQL repository

// Note: getSentContactRequests, getReceivedContactRequests, and deleteContact methods
// are now handled directly by the PostgreSQL repository

// authMiddleware provides authentication middleware
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.AuthMiddleware(s.oryClient, s.logger)
}

// fetchUserData fetches user information directly from the database
func (s *Service) fetchUserData(ctx context.Context, userID string) (*UserData, error) {
	// Query user data directly from PostgreSQL database
	query := `
		SELECT id, username, email, first_name, last_name, avatar_url
		FROM users
		WHERE id = $1 AND is_active = true
	`

	var userData UserData
	err := s.db.Pool.QueryRow(ctx, query, userID).Scan(
		&userData.ID,
		&userData.Username,
		&userData.Email,
		&userData.FirstName,
		&userData.LastName,
		&userData.AvatarURL,
	)

	if err != nil {
		return nil, database.HandlePgxError(err, "users")
	}

	return &userData, nil
}

// UserData represents user information from the user service
type UserData struct {
	ID        string  `json:"id"`
	Username  string  `json:"username"`
	Email     string  `json:"email"`
	FirstName string  `json:"first_name"`
	LastName  string  `json:"last_name"`
	AvatarURL *string `json:"avatar_url"`
}

// Note: convertDatabaseContact method removed - no longer needed with PostgreSQL repository

// buildContactResponse creates a ContactResponse with real user data
func (s *Service) buildContactResponse(ctx context.Context, contact *Contact) (*ContactResponse, error) {
	// Fetch sender data
	senderData, err := s.fetchUserData(ctx, contact.RequesterID)
	if err != nil {
		s.logger.Warn("Failed to fetch sender data",
			zap.String("sender_id", contact.RequesterID),
			zap.Error(err))
		// Fallback to basic data
		senderData = &UserData{
			ID:        contact.RequesterID,
			FirstName: "Unknown",
			LastName:  "User",
		}
	}

	// Fetch receiver data
	receiverData, err := s.fetchUserData(ctx, contact.RecipientID)
	if err != nil {
		s.logger.Warn("Failed to fetch receiver data",
			zap.String("receiver_id", contact.RecipientID),
			zap.Error(err))
		// Fallback to basic data
		receiverData = &UserData{
			ID:        contact.RecipientID,
			FirstName: "Unknown",
			LastName:  "User",
		}
	}

	return &ContactResponse{
		ID:                contact.ID,
		SenderId:          contact.RequesterID,
		SenderName:        senderData.FirstName + " " + senderData.LastName,
		SenderAvatarUrl:   senderData.AvatarURL,
		ReceiverId:        contact.RecipientID,
		ReceiverName:      receiverData.FirstName + " " + receiverData.LastName,
		ReceiverAvatarUrl: receiverData.AvatarURL,
		SentAt:            contact.CreatedAt,
		Status:            contact.Status,
		Message:           contact.Message,
		RespondedAt:       contact.AcceptedAt,
	}, nil
}

// sendContactRequestNotification sends a notification to the recipient about the contact request
func (s *Service) sendContactRequestNotification(ctx context.Context, recipientID, requesterID string) error {
	// Publish MQTT event for real-time updates
	if err := s.publishContactRequestEvent(recipientID, requesterID, "", "received"); err != nil {
		s.logger.Warn("Failed to publish MQTT contact request event", zap.Error(err))
		// Don't fail the request if MQTT publishing fails
	}

	// Create notification request payload for legacy HTTP notification
	notificationReq := map[string]interface{}{
		"user_id": recipientID,
		"type":    "contact_request_received",
		"title":   "Contact Request",
		"message": "You have a new contact request!",
		"data": map[string]interface{}{
			"requester_id": requesterID,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(notificationReq)
	if err != nil {
		return fmt.Errorf("failed to marshal notification request: %w", err)
	}

	// Send HTTP request to notification service
	notificationURL := fmt.Sprintf("http://localhost:4000/api/v1/notifications")
	req, err := http.NewRequestWithContext(ctx, "POST", notificationURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create notification request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send notification request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("notification service returned status %d", resp.StatusCode)
	}

	s.logger.Info("Contact request notification sent successfully",
		zap.String("recipient_id", recipientID),
		zap.String("requester_id", requesterID))

	return nil
}

// publishContactRequestEvent publishes a contact request event to MQTT
func (s *Service) publishContactRequestEvent(userID, senderID, requestID, action string) error {
	if s.mqttClient == nil || !s.mqttClient.IsConnected() {
		s.logger.Debug("MQTT client not available, skipping contact request event publishing")
		return nil
	}

	event := ContactRequestEvent{
		EventType:  "contact_request",
		EventID:    uuid.New().String(),
		UserID:     userID,
		RequestID:  requestID,
		Action:     action,
		SenderID:   senderID,
		ReceiverID: userID,
		Timestamp:  time.Now(),
	}

	payload, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal contact request event: %w", err)
	}

	// Publish to unified requests topic (single source of truth)
	topic := fmt.Sprintf("hopen/requests/%s", userID)
	token := s.mqttClient.Publish(topic, 1, false, payload) // QoS 1 for reliable delivery

	if token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to publish contact request event: %w", token.Error())
	}

	s.logger.Info("Contact request event published successfully",
		zap.String("topic", topic),
		zap.String("action", action),
		zap.String("user_id", userID),
		zap.String("sender_id", senderID))

	return nil
}

// sendContactRequestAcceptedNotification sends a notification when a contact request is accepted
func (s *Service) sendContactRequestAcceptedNotification(ctx context.Context, requesterID, acceptedByUserID string) error {
	// Publish MQTT event for real-time updates
	if err := s.publishContactRequestEvent(requesterID, acceptedByUserID, "", "accepted"); err != nil {
		s.logger.Warn("Failed to publish MQTT contact request accepted event", zap.Error(err))
		// Don't fail the request if MQTT publishing fails
	}

	notificationReq := map[string]interface{}{
		"user_id": requesterID,
		"type":    "contact_request_accepted",
		"title":   "Contact Request Accepted",
		"message": "Your contact request was accepted!",
		"data": map[string]interface{}{
			"accepted_by_user_id": acceptedByUserID,
		},
	}

	jsonData, err := json.Marshal(notificationReq)
	if err != nil {
		return fmt.Errorf("failed to marshal notification request: %w", err)
	}

	notificationURL := fmt.Sprintf("http://localhost:4000/api/v1/notifications")
	req, err := http.NewRequestWithContext(ctx, "POST", notificationURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create notification request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send notification request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("notification service returned status %d", resp.StatusCode)
	}

	return nil
}

// sendContactRequestDeclinedNotification sends a notification when a contact request is declined
func (s *Service) sendContactRequestDeclinedNotification(ctx context.Context, requesterID, declinedByUserID string) error {
	// Publish MQTT event for real-time updates
	if err := s.publishContactRequestEvent(requesterID, declinedByUserID, "", "declined"); err != nil {
		s.logger.Warn("Failed to publish MQTT contact request declined event", zap.Error(err))
		// Don't fail the request if MQTT publishing fails
	}

	notificationReq := map[string]interface{}{
		"user_id": requesterID,
		"type":    "contact_request_declined",
		"title":   "Contact Request Declined",
		"message": "Your contact request was declined.",
		"data": map[string]interface{}{
			"declined_by_user_id": declinedByUserID,
		},
	}

	jsonData, err := json.Marshal(notificationReq)
	if err != nil {
		return fmt.Errorf("failed to marshal notification request: %w", err)
	}

	notificationURL := fmt.Sprintf("http://localhost:4000/api/v1/notifications")
	req, err := http.NewRequestWithContext(ctx, "POST", notificationURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create notification request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send notification request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("notification service returned status %d", resp.StatusCode)
	}

	return nil
}


