name: Hopen Backend CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'hopenbackend/**'
      - '.github/workflows/backend-ci-cd.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'hopenbackend/**'
      - '.github/workflows/backend-ci-cd.yml'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/hopen-backend

jobs:
  # Test Job
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test123
          POSTGRES_USER: test
          POSTGRES_DB: hopen_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: valkey/valkey:7.2-alpine
        options: >-
          --health-cmd "valkey-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.23'

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      working-directory: ./hopenbackend
      run: go mod download

    - name: Run tests
      working-directory: ./hopenbackend
      env:
        DATABASE_URL: postgres://test:test123@localhost:5432/hopen_test?sslmode=disable
        REDIS_URL: redis://localhost:6379
      run: |
        go test -v -race -coverprofile=coverage.out ./...
        go tool cover -html=coverage.out -o coverage.html

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./hopenbackend/coverage.out
        flags: backend
        name: backend-coverage

    - name: Run integration tests
      working-directory: ./testing/integration
      env:
        DATABASE_URL: postgres://test:test123@localhost:5432/hopen_test?sslmode=disable
        REDIS_URL: redis://localhost:6379
      run: |
        go mod download
        go test -v ./...

  # Security Scan Job
  security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: './hopenbackend'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run Gosec Security Scanner
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: './hopenbackend/...'

  # Build Job
  build:
    needs: [test, security]
    runs-on: ubuntu-latest
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: ./hopenbackend
        file: ./hopenbackend/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

    - name: Scan published image with Trivy
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'image'
        image-ref: ${{ steps.meta.outputs.tags }}
        format: 'table'
        exit-code: '1'

  # Load Testing Job
  load-test:
    needs: [build]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.23'

    - name: Start test environment
      working-directory: ./hopenbackend
      run: |
        docker-compose -f docker-compose.yml up -d
        sleep 60  # Wait for services to be ready

    - name: Run load tests
      working-directory: ./hopenbackend/testing/load-testing
      run: |
        go mod download
        chmod +x run-load-test.sh
        ./run-load-test.sh

    - name: Upload load test results
      uses: actions/upload-artifact@v3
      with:
        name: load-test-results
        path: ./hopenbackend/testing/load-testing/results/

    - name: Cleanup test environment
      if: always()
      working-directory: ./hopenbackend
      run: docker-compose down -v

  # Deploy to Staging
  deploy-staging:
    needs: [build, load-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      env:
        STAGING_HOST: ${{ secrets.STAGING_HOST }}
        STAGING_USER: ${{ secrets.STAGING_USER }}
        STAGING_SSH_KEY: ${{ secrets.STAGING_SSH_KEY }}
        IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
      run: |
        echo "$STAGING_SSH_KEY" > staging_key
        chmod 600 staging_key
        
        # Deploy to staging server
        ssh -i staging_key -o StrictHostKeyChecking=no $STAGING_USER@$STAGING_HOST << EOF
          cd /opt/hopen-backend
          docker-compose pull
          docker-compose up -d
          docker system prune -f
        EOF
        
        rm staging_key

    - name: Run staging health check
      env:
        STAGING_URL: ${{ secrets.STAGING_URL }}
      run: |
        sleep 30  # Wait for deployment
        curl -f $STAGING_URL/health || exit 1

  # Deploy to Production
  deploy-production:
    needs: [build, load-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      env:
        PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
        PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
        PRODUCTION_SSH_KEY: ${{ secrets.PRODUCTION_SSH_KEY }}
        IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
      run: |
        echo "$PRODUCTION_SSH_KEY" > production_key
        chmod 600 production_key
        
        # Blue-Green deployment to production
        ssh -i production_key -o StrictHostKeyChecking=no $PRODUCTION_USER@$PRODUCTION_HOST << EOF
          cd /opt/hopen-backend
          
          # Pull new image
          docker-compose pull
          
          # Start new containers (blue-green deployment)
          docker-compose -f docker-compose.yml -f docker-compose.blue-green.yml up -d
          
          # Health check
          sleep 30
          curl -f http://localhost:4001/health || exit 1
          
          # Switch traffic (update load balancer)
          ./scripts/switch-traffic.sh
          
          # Cleanup old containers
          docker-compose -f docker-compose.old.yml down
          docker system prune -f
        EOF
        
        rm production_key

    - name: Run production health check
      env:
        PRODUCTION_URL: ${{ secrets.PRODUCTION_URL }}
      run: |
        sleep 30  # Wait for deployment
        curl -f $PRODUCTION_URL/health || exit 1

    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "🚀 Hopen Backend deployed to production successfully!"
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  # Cleanup Job
  cleanup:
    needs: [deploy-staging, deploy-production]
    runs-on: ubuntu-latest
    if: always()

    steps:
    - name: Clean up old images
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        # Keep only the last 10 images
        gh api \
          --method GET \
          -H "Accept: application/vnd.github+json" \
          /orgs/${{ github.repository_owner }}/packages/container/hopen-backend/versions \
          --jq '.[10:] | .[] | .id' | \
        while read version_id; do
          gh api \
            --method DELETE \
            -H "Accept: application/vnd.github+json" \
            /orgs/${{ github.repository_owner }}/packages/container/hopen-backend/versions/$version_id
        done
