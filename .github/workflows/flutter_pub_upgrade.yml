name: Flutter Pub Upgrade

on:
  schedule:
    - cron: '0 3 * * 1' # Every Monday at 03:00 UTC
  workflow_dispatch:

jobs:
  pub-upgrade:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: stable
          cache: true

      - name: Upgrade dependencies
        run: |
          flutter pub upgrade --major-versions
          dart pub outdated --no-dev

      - name: Run static analysis & tests
        run: |
          flutter pub get
          flutter analyze --no-fatal-warnings
          flutter test --coverage

      # Only commit if previous steps succeeded and code changed
      - name: Check for changes
        id: changes
        run: |
          echo "changed=$(git status --porcelain | wc -l)" >> $GITHUB_OUTPUT

      - name: Commit and push changes
        if: steps.changes.outputs.changed != '0'
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: 'chore(pub): automated flutter pub upgrade'
          branch: pub-upgrade-automation
          create_branch: true
          push_options: '--force'

      - name: Create Pull Request
        if: steps.changes.outputs.changed != '0'
        uses: peter-evans/create-pull-request@v6
        with:
          branch: pub-upgrade-automation
          title: 'chore(pub): automated dependency upgrade'
          body: This PR was created automatically by the weekly workflow to keep Flutter dependencies up to date and all tests/analysis are green. 