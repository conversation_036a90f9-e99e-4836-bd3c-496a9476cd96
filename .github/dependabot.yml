version: 2
updates:
  - package-ecosystem: "pub"
    directory: "/hopen" # Flutter app root containing pubspec.yaml
    schedule:
      interval: "weekly"
      day: "monday"
      time: "03:00"
    open-pull-requests-limit: 5
    labels:
      - "dependencies"
      - "flutter"

  - package-ecosystem: "go"
    directory: "/hopenbackend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "04:00"
    open-pull-requests-limit: 5
    labels:
      - "dependencies"
      - "go" 